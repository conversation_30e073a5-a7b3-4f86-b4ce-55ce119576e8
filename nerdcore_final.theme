# nerdcore_final.theme
# A complete and corrected nerd/geek theme with a high-contrast palette and all format definitions from tahio.theme.

replaces = { ":()" = "%K$*%n"; };

abstracts = {
  # General
  line_start = "%B»%n ";
  timestamp = "%g➜ %b$*%n%|";
  error = "%RError: $*$*%n";
  hilight = "%Y$*%n";
  comment = "%B[%n$*%B]%N";
  reason = "%B(%n$*%K)%N";

  # Channels and Nicks
  channel = "%g%_$*%_";
  nick = "%b$*%n";
  ownnick = "%Y%_$*%_";
  nickhost = "$*";
  channick = "%g$*%n";
  chanhost = " %B[%g{nickhost $*}%B]%n";
  channick_hilight = "%Y$*%n";
  chanhost_hilight = " %B[{nickhost $*}]%B";
  channelhilight = "%g$*%n";

  # Messages
  msgnick = "%b$0%g$1-%n%B:%n ";
  ownmsgnick = "%y$0%Y$1-%n%B:%n ";
  pubmsgnick = "{msgnick $0 $1-}";
  pubnick = "%_$*%_";
  pubmsgmenick = "{msgnick $0 %_$1-%_%n}";
  menick = "%Y$*%n";
  pubmsghinick = "{msgnick $1 $0$2-}";
  msgchannel = ":%g%_$*%_";
  privmsg = "%B[%g$0%B(%b{nickhost $1-}%B)]%n ";
  ownprivmsg = "%B[%Yquery%B(%y$1-%B)]%n ";
  ownprivmsgnick = "%B<%y$*%n%B>%N ";
  ownprivnick = "%_$*%_";
  privmsgnick = "%B<%b$*%n%B>%N ";

  # Actions
  action = "%Y*%n";
  ownaction = "{action } %g$0 $1-";
  pvtaction = "%B[%gquery%B(%b$0%B)]%n {action } %b->%n $1-";
  pvtaction_query = "%B* $* {action } ";
  pubaction = "{action } %g$* } %b";

  # System Messages
  mode = "%B<%g%_$*%B%_%B>%N";
  ban = "$*";
  netsplit = "%G$*";
  netjoin = "%G$*";
  names_nick = "%b$0%w$[9]1-%n ";
  names_users = "(%g$0%w(%b$1%W))";
  names_channel = "%b$*";
  dcc = "%b$0%n $1 $3 $4 %g$5 $6 $7 $8-%n";
  dccfile = "%_$*%_";
  dccownmsg = "%B[%gdcc%n(%b$*%n)]%n ";
  dccownnick = "%g$*%n";
  dccownaction = "%B[%gdcc%g(%b$0%n)]%n {action } %w->%n $1-";
  dccmsg = "%B[%gdcc%n(%b$*%n)]%n ";
  dccquerynick = "%b$*%n";
  dccaction = "%B[%gdcc%n(%g$*)]%n {action }%n %|";

  # Whois
  whois = "$[12]0 : $1-";
  whois1 = "$[12]0 : $1-";
  whois2 = "$[12]0 : $1-";
  whois3 = "$[12]0 : $1-";

  # Notices
  ownnotice = "%B[%n%gnotice%n%B(%b$1-%n%B)]%n ";
  notice = "%g» %GNotice %B>%g>%b> %n{nick $0}: $1";
  pubnotice_channel = ":$*";
  pvtnotice_host = "%B(%n%g$*%n%B)%n";
  servernotice = "{notice $*}";

  # CTCP
  ownctcp = "%B[%N%g$0%n%B(%b$1-%n%B)]%n ";
  ctcp = "%B>%n>%g>%n {nick $0} %g$1%n $2 %g$3%n $4 $5 %g$6%n";

  # Wallops
  wallop = " $*: ";
  wallop_nick = " $*";
  wallop_action = " * $* ";

  # Status Bar
  sb_background = "%b";
  sb_topic_bg = "%B";
  sb = "%g ➜ %n$0-%g ☣ %n";
  prompt = "%B»%n ";
  sbmode = "$0-";
  sbaway = " %Y(away)%n";
  sbservertag = ":$0 %n(%gchange with ^X%n)";
  sbmore = "  %r<%R<%k< %nmore %k>%R>%r>  ";
  sblag = "{sb Lagging %r$0-%K seconds!}";
  sb_default_bg = "%4";
  sb_act_sep = "%K/";
  sb_act_text = "%Y$*";
  sb_act_msg = "%g$*";
  sb_act_hilight = "%R$*";
  sb_act_hilight_color = "%b$0$1-%n";
  sb_info1_bg = "%g";
  sb_window_bg = "%b%W";
  sb_window2_bg = "%b%W";
  sb_info2_bg = "%g";
  sb_usercount = "{sb %gN%wetwork:%n %K$tag }{sb %gU%wsers: %R$0 %K$1-";
  sb_uc_normal = "%gN%wormal %Y$*%R]";
  sb_uc_ops = "%R[%gIRC%wOpers %_%y$mh_opercount  ➜  %gO%wpers %_%Y$*%g  ➜ ";
  sb_uc_voices = "%gV%woice %Y$*%g  ➜ ";

  # nm2 script settings
  nickalign = "";
  nickcolor = "%n";
  nicktrunc = "";
  cumode_space = " ";

  # Colors
  # %g, %G - green, bold green (terminal green)
  # %b, %B - blue, bold blue (electric blue)
  # %y, %Y - yellow, bold yellow (vibrant orange)
  # %r, %R - red, bold red (errors)
  # %k, %K - black
  # %w, %W - white
  # %m, %M - magenta
  # %c, %C - cyan
  # %n - default
};

formats = {
  "fe-common/core" = {
    join = "%G»»%n {channick_hilight %g$0} {chanhost_hilight %g$1}";
    part = "%Y««%n {channick_hilight %y$0} {chanhost_hilight %K$1} %B{reason $3}";
    kick = "%R✗%n {channick_hilight %B$0} by {nick $2} %B{reason $3}";
    quit = "%r↤%n {channick_hilight %y$0}%n {chanhost_hilight %K$1}%n %B{reason $2}";
    notice = "{notice $0 $1}";
    quit_once = "{channel $3} {channick $0} {chanhost $1} {reason $2}";
    servertag = "%b[%W$0%b] ";
    invite = "%g✉%n {nick %b$0} to {channel %g$1}";
    new_topic = "%g✍️ %nTopic was changed by %b{channick $0} in {channel %g$1} to: $2";
    topic_unset = "%g💨 %nTopic for %n{channel %g$1} %Wunset by %n{channick %b$0}";
    your_nick_changed = "👤 You are now %Y{nick $1} ☣ ";
    nick_changed = "👤 {channick %b$0} %wis now %Y{%channick_hilight $1}";
    talking_in = "🗣️ You are now talking in {channel %g$0}";
    not_in_channels = "🤷 You are not on any channels";
    names = "{names_users %W ➜ Users ☣ ) (channel {names_channel $0}} ➜ (%G$1) ☣ ";
    names_nick = "{names_nick $0 $1}";
    endofnames = "{channel %w$0}: %WTotal of {hilight %B((%G$1%R))%W} %wnicks%n {comment %WOps {hilight %W((%G$2%W))%W}, Voice %B(({hilight %G$4%R))%W}, Normal %B(({hilight %G$5%R))%W}";

    pubmsg = "{pubmsgnick $nickalign$2 {pubnick $nickcolor$0$nicktrunc}}$1";
    own_msg = "{ownmsgnick $nickalign$2 {ownnick $nickcolor$0$nicktrunc}}$1";
    own_msg_channel = "{ownmsgnick $nickalign$3 {ownnick $nickcolor$0$nicktrunc}{msgchannel $1}}$2";
    pubmsg_me = "{pubmsgmenick $nickalign$2 {menick $0$nicktrunc}}$1";
    pubmsg_me_channel = "{pubmsgmenick $nickalign$3 {menick $0$nicktrunc}{msgchannel $1}}$2";
    pubmsg_hilight = "{pubmsghinick $0 $nickalign$3 $1$nicktrunc}$2";
    pubmsg_hilight_channel = "{pubmsghinick $0 $nickalign$4 $1$nicktrunc{msgchannel $2}}$3";
    pubmsg_channel = "{pubmsgnick $nickalign$3 {pubnick $nickcolor$0$nicktrunc}{msgchannel $1}}$2";
    own_msg_private_query = "{ownprivmsgnick $nickalign{ownprivnick $nickcolor$2$nicktrunc}}$1";
    msg_private_query = "{privmsgnick $nickalign$nickcolor$0$nicktrunc}$2";
  };
  "fe-common/irc" = {
    inviting = "%g✉%n Inviting {nick %b$0} to {channel %g$1}";
    topic_info = "%g📜 %nTopic set by %N{channick %b$0} {comment $1}";
    server_chanmode_change = "{netsplit %CServerMode%n}/{channelhilight $0} {mode $1} by {nick $2}";
    whois = "{nick $0} ({nickhost $1%R@%n$2}) {comment $4}%:{whois %_i%_rcname $3}";
    whowas = "           %WWHOWAS%K       %n%:%r*%n {nick $0} ({nickhost $1%R@%n$2})%:{whois %_i%_rcname $3}";
    whois_idle = "{whois3 %_i%_dle %|$1 days $2 hours $3 mins $4 secs}";
    whois_idle_signon = "{whois3 %_i%_dle %|$1 days $2 hours $3 mins $4 secs (signon: $5)}";
    whois_server = "{whois2 %_s%_erver %|$1 ($2)}";
    whois_oper = "{whois3 %_o%_perator {hilight $1}}";
    whois_registered = "{whois %_a%_uth has registered this nick}";
    whois_help = "{whois %_d%_uty is available for help}";
    whois_modes = " {whois %_m%_odes $1}";
    whois_realhost = "{whois %_h%_ostname $1-}";
    whois_usermode = "{whois %_u%_sermode $1}";
    whois_channels = "{whois %_c%_hannels %|$1}";
    whois_away = "{whois %_a%_way %W%|$1}";
    whois_special = "{whois  %|{hilight $1}}";
    end_of_whois = "%WEnd of WHOIS ☣ ";
    end_of_whowas = "%wEnd of WHOWAS ☣ ";
    whois_not_found = "%wThere is no such Nick:%R.%w {nick %W$0}";
    who = "{channelhilight %C$[!10]0%n} %|{nick $[!9]1} %c$[!3]2%n $[!2]3 $4%R@%n$5 :: $6";
    ctcp_requested = "requested $2 from {nick $3} {ctcp {hilight $0} {comment $1}}";
    own_action = "$nickalign{ownaction $0$nicktrunc}$1";
    action_private = "$nickalign{pvtaction $0$nicktrunc}$2";
    action_private_query = "$nickalign{pvtaction_query $0$nicktrunc}$2";
    action_public = "$nickalign{pubaction $0$nicktrunc}$1";
  };
  "fe-common/irc/dcc" = {
    own_dcc = "{dccownmsg {dccownnick $1}}$2";
    own_dcc_action = "{dccownaction_target $0 $1}$2";
    own_dcc_action_query = "{dccownaction $0}$2";
    own_dcc_ctcp = "{ownctcp ctcp $0}$1 $2";
    dcc_msg = "{dccmsg $0}$1";
    action_dcc = "{dccaction $0}$1";
    action_dcc_query = "{dccaction $0}$1";
    own_dcc_query = "{dccownmsg {dccownnick $0}}$2";
    dcc_msg_query = "{dccmsg $0}$1";
    dcc_ctcp = "{dcc %W>>> DCC CTCP {hilight $1} received from {hilight $0}: $2}";
  };
  nm2 = { neat_pad_char = " "; };
  "Irssi::Script::adv_windowlist" = {
    awl_display_key_active = "%B» $N %g$H$C$S";
    awl_display_key = "  %b$N %g$H$C$S";
    awl_viewer_item_bg = "%N";
    awl_display_header = "";
    awl_display_nokey = "  %b$N %g$H$C$S";
    awl_display_nokey_visible = "👁 %b$N %g$H$C$S";
    awl_display_key_visible = "👁 %b$N %g$H$C$S";
    awl_display_nokey_active = "%B» $N %g$H$C$S";
    awl_data_level_none = "%g";
    awl_data_level_low = "%y";
    awl_data_level_medium = "%Y";
    awl_data_level_high = "%R";
  };
  "Irssi::Script::nm2" = { neat_pad_char = " "; };
};
