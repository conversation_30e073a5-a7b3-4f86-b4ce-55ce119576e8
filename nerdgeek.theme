
# nerdgeek.theme
# A theme for the discerning nerd, inspired by classic terminals and modern high-contrast aesthetics.

replaces = {
  ":()" = "%K$*%n";
};

abstracts = {
  # General
  line_start = "%B»%n ";
  timestamp = "%g➜ %b$*%n%|";
  error = "%RError: $*$*%n";
  hilight = "%Y$*%n";

  # Channels and Nicks
  channel = "%g%_$*%_";
  nick = "%b$*%n";
  ownnick = "%Y%_$*%_";
  nickhost = "$*";
  channick = "%g$*%n";
  chanhost = " %B[%g{nickhost $*}%B]%n";
  channick_hilight = "%Y$*%n";
  chanhost_hilight = " %B[{nickhost $*}]%B";

  # Messages
  msgnick = "%b$0%n%B:%n ";
  ownmsgnick = "%Y$0%n%B:%n ";
  pubmsgnick = "{msgnick $0 $1-}";
  pubnick = "%_$*%_";
  pubmsgmenick = "{msgnick $0 %_$1-%_%n}";
  menick = "%Y$*%n";
  pubmsghinick = "{msgnick $1 $0$2-}";
  msgchannel = ":%g%_$*%_";
  privmsg = "%B[%g$0%B(%b{nickhost $1-}%B)]%n ";
  ownprivmsg = "%B[%Yquery%B(%y$1-%B)]%n ";

  # Actions
  action = "%Y*%n";
  ownaction = "{action} %g$0 $1-";
  pvtaction = "%B[%gquery%B(%b$0%B)]%n {action} %b->%n $1-";
  pubaction = "{action} %g$* } %b";

  # System Messages
  join = "%G»»%n {channick_hilight %g$0} {chanhost $1}";
  part = "%Y««%n {channick_hilight %y$0} {chanhost $1} {reason $3}";
  kick = "%R✗%n {channick_hilight %B$1} kicked from {channel %B$0} by {nick %B$2} {reason $3}";
  quit = "%r↤%n {channick %y$0} {chanhost $1} {reason $2}";
  invite = "%g✉%n {nick %b$0} invites you to {channel %g$1}";
  topic = "📜 Topic for {channel %g$0} is: $1";
  new_topic = "✍️ {nick %b$0} changed the topic in {channel %g$1} to: $2";
  topic_unset = "💨 {nick %b$0} unset the topic in {channel %g$1}";
  nick_changed = "👤 {channick %b$0} is now {channick_hilight %Y$1}";
  your_nick_changed = "👤 Your nick is now %Y{nick $1}";
  mode = "🔧 Mode {channel %g$0} [%Y$1%n] by {nick %b$2}";

  # Status Bar
  sb_background = "%b";
  sb_topic_bg = "%B";
  sb = "%g$0-%n";
  prompt = "%B»%n ";
  sbmode = "$0-";
  sbaway = " %Y(away)%n";
  sbservertag = "$0";
  sbmore = " %B…%n";
  sblag = " %Rlag: $0-%n";
  sb_act_sep = "/";
  sb_act_text = "%Y$*";
  sb_act_msg = "%g$*";
  sb_act_hilight = "%R$*";

  # Colors
  # %g, %G - green, bold green (terminal green)
  # %b, %B - blue, bold blue (electric blue)
  # %y, %Y - yellow, bold yellow (vibrant orange)
  # %r, %R - red, bold red (errors)
};

formats = {
  "fe-common/core" = {
    join = "{join $0 $1}";
    part = "{part $0 $1 $3}";
    kick = "{kick $0 $1 $2 $3}";
    quit = "{quit $0 $1 $2}";
    invite = "{invite $0 $1}";
    topic = "{topic $0 $1}";
    new_topic = "{new_topic $0 $1 $2}";
    topic_unset = "{topic_unset $0 $1}";
    your_nick_changed = "{your_nick_changed $0 $1}";
    nick_changed = "{nick_changed $0 $1}";
    talking_in = "🗣️ You are now talking in {channel $0}";
    not_in_channels = "🤷 You are not on any channels";
    names = "👥 Users in {channel $0}: $1";
    endofnames = "Total of $1 nicks ($2 ops, $4 voices)";
    mode = "{mode $0 $1 $2}";

    pubmsg = "{pubmsgnick $2 {pubnick $0}}$1";
    own_msg = "{ownmsgnick $2 {ownnick $0}}$1";
    own_msg_channel = "{ownmsgnick $3 {ownnick $0}{msgchannel $1}}$2";
    pubmsg_me = "{pubmsgmenick $2 {menick $0}}$1";
    pubmsg_me_channel = "{pubmsgmenick $3 {menick $0}{msgchannel $1}}$2";
    pubmsg_hilight = "{pubmsghinick $0 $3 $1}$2";
    pubmsg_hilight_channel = "{pubmsghinick $0 $4 $1{msgchannel $2}}$3";
    pubmsg_channel = "{pubmsgnick $3 {pubnick $0}{msgchannel $1}}$2";
    own_msg_private_query = "{ownprivmsgnick {ownprivnick $2}}$1";
    msg_private_query = "{privmsgnick $0}$2";
  };
};
