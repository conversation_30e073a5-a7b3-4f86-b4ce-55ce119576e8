# Tahio.theme Baseline Audit

## File Overview
- **Original file**: tahio.theme
- **Total lines**: 226
- **Structure**: Contains `replaces`, `abstracts`, and `formats` sections

---

## 1. REPLACES Section (Line 1)
```irc
replaces = { ":())" = "%K$*%n"; };
```
- **Placeholder**: `$*` (represents all arguments)
- **Color codes**: `%K` (black), `%n` (default)

---

## 2. ABSTRACTS Section (Lines 2-138)

### Key Abstract Definitions with Placeholders & Colors:

#### Basic Elements
- **line_start**: `"%R➜ "` → Color: `%R` (red), Icon: `➜`
- **timestamp**: `"%G ➜ %C$*%K ⎢%n"` → Colors: `%G` (green), `%C` (cyan), `%K` (black), `%n` (default), Icons: `➜`, `⎢`
- **hilight**: `"%W$*"` → Color: `%W` (white), Placeholder: `$*`
- **error**: `"%r$*%n"` → Color: `%r` (red), Placeholder: `$*`
- **channel**: `"%B%_$*%_"` → Color: `%B` (blue), Formatting: `%_` (underline)
- **channel2**: `"%W$C%n"` → Color: `%W` (white), Placeholder: `$C`
- **nick**: `"%n$*%n"` → Color: `%n` (default)
- **nickhost**: `"$*"` → Placeholder: `$*`
- **server**: `"%g$*%N"` → Colors: `%g` (gray), `%N` (normal)
- **comment**: `"%W[%n$*%W]%N"` → Colors: `%W` (white), `%n` (default), `%N` (normal)
- **reason**: `"%W(%n$*%K)%N"` → Colors: `%W` (white), `%n` (default), `%K` (black), `%N` (normal)
- **mode**: `"%W<%B%_$*%W_%W>%N"` → Colors: `%W` (white), `%B` (blue), Formatting: `%_` (underline)

#### Nickname Handling
- **channick_hilight**: `"%B$*%n"` → Color: `%B` (blue)
- **channick_hilight2**: `"%W$*%n"` → Color: `%W` (white)
- **chanhost_hilight**: `"%w<{nickhost $*}%w>"` → Color: `%w` (light gray)
- **channick**: `"%g$*%n"` → Color: `%g` (gray)
- **chanhost**: `"%W<%n%g{nickhost $*}%n%W>%n"` → Colors: `%W` (white), `%n` (default), `%g` (gray)
- **channelhilight**: `"%g$*%n"` → Color: `%g` (gray)

#### Message Formatting
- **msgnick**: `"%n%K$0%W$1-%_%W:%n%|"` → Placeholders: `$0`, `$1-`, Colors: `%n`, `%K`, `%W`, Formatting: `%_`, `%|`
- **ownmsgnick**: `"%n%K$0%W$1-%_%W:%n%|"` → Same as msgnick
- **ownnick**: `"%y%_$*%_"` → Color: `%y` (yellow), Formatting: `%_` (underline)
- **pubmsgnick**: `"{msgnick $0 $1-}"` → References msgnick abstract
- **pubnick**: `"%_$*%_"` → Formatting: `%_` (underline)
- **pubmsgmenick**: `"{msgnick $0 %_$1-%_%n}"` → Mixed formatting
- **menick**: `"%R$*%n"` → Color: `%R` (red)
- **pubmsghinick**: `"{msgnick $1 $0$2-}"` → Complex placeholder usage

#### Private Messages
- **privmsg**: `"%K[%n%P$0%n%K(%n{nickhost $1-}%p%n%K)]%n "` → Colors: `%K`, `%n`, `%P`, `%p`
- **ownprivmsg**: `"%K[%n%c%n%K(%C$1-%n%K)]%n "` → Colors: `%K`, `%n`, `%c`, `%C`
- **ownprivmsgnick**: `"%W<%b$*%n%W>%N "` → Colors: `%W`, `%b`, `%n`, `%N`
- **ownprivnick**: `"%_$*%_"` → Formatting: `%_` (underline)
- **privmsgnick**: `"%W<%_%C$*%n%W>%N "` → Colors: `%W`, `%C`, `%n`, `%N`

#### Actions
- **action**: `"%K*%n"` → Color: `%K` (black), Icon: `*`
- **ownaction**: `"{action } %W$0 $1-"` → Colors: `%W` (white)
- **pvtaction**: `"%K[%cquery%n(%C$0%n)]%n {action } %w->%n $1-"` → Colors: `%K`, `%c`, `%n`, `%C`, `%w`, Icon: `*`, `->`
- **pvtaction_query**: `"%W* $* {action } "` → Color: `%W`, Icon: `*`
- **pubaction**: `"{action } %W$* } %g"` → Colors: `%W`, `%g`

#### WHOIS Information
- **whois**: `"$[12]0 : $1-"` → Format specifier: `$[12]0` (12-char width)
- **whois1**: `"$[12]0 : $1-"` → Same format
- **whois2**: `"$[12]0 : $1-"` → Same format
- **whois3**: `"$[12]0 : $1-"` → Same format

#### Notices & CTCP
- **ownnotice**: `"%K[%n%cnotice%n%K(%C$1-%n%K)]%n "` → Colors: `%K`, `%n`, `%c`, `%C`
- **notice**: `"%R➜ %GNotice %K>%W>%B> %n{nick $0}: $1"` → Colors: `%R`, `%G`, `%K`, `%W`, `%B`, `%n`, Icon: `➜`, `>`
- **pubnotice_channel**: `":$*"` → Simple placeholder
- **pvtnotice_host**: `"%K(%n%c$*%n%K)%n"` → Colors: `%K`, `%n`, `%c`
- **servernotice**: `"{notice $*}"` → References notice abstract
- **ownctcp**: `"%K[%N%c$0%n%K(%C$1-%n%K)]%n "` → Colors: `%K`, `%N`, `%c`, `%n`, `%C`
- **ctcp**: `"%K>%n>%W>%n {nick $0} %g$1%n $2 %W$3%n $4 $5 %g$6%n"` → Multiple placeholders and colors

#### Network Events
- **wallop**: `" $*: "` → Simple format
- **wallop_nick**: `" $*"` → Simple format
- **wallop_action**: `" * $* "` → Icon: `*`
- **netsplit**: `"%G$*"` → Color: `%G` (green)
- **netjoin**: `"%G$*"` → Color: `%G` (green)

#### Names List
- **names_nick**: `"%B$0%w$[9]1-%n "` → Colors: `%B`, `%w`, `%n`, Format: `$[9]1-`
- **names_users**: `"(%c$0%w(%B$1%W))"` → Colors: `%c`, `%w`, `%B`, `%W`
- **names_channel**: `"%B$*"` → Color: `%B` (blue)

#### DCC
- **dcc**: `"%C$0%n $1 $3 $4 %c$5 $6 $7 $8-%n"` → Colors: `%C`, `%n`, `%c`
- **dccfile**: `"%_$*%_"` → Formatting: `%_` (underline)
- **dccownmsg**: `"%K[%gdcc%n(%C$*%n)]%n "` → Colors: `%K`, `%g`, `%n`, `%C`
- **dccownnick**: `"%c$*%n"` → Colors: `%c`, `%n`
- **dccownaction**: `"%K[%gdcc%g(%C$0%n)]%n {action } %w->%n $1-"` → Colors and icon: `->`
- **dccmsg**: `"%K[%cdcc%n(%C$*%n)]%n "` → Colors: `%K`, `%c`, `%n`, `%C`
- **dccquerynick**: `"%C$*%n"` → Colors: `%C`, `%n`
- **dccaction**: `"%K[%gdcc%n(%g$*)]%n {action }%n %|"` → Colors and formatting

#### Status Bar Elements
- **sb_background**: `"%0"` → Background color
- **sb_topic_bg**: `"%G%W"` → Colors: `%G`, `%W`
- **sb**: `"%W ➜ %n$0-%W ☣ %n"` → Colors: `%W`, `%n`, Icons: `➜`, `☣`
- **prompt**: `"%K<%n%_$0%n%K>%n "` → Colors: `%K`, `%n`, Formatting: `%_`
- **sbmode**: `"$0-"` → Simple placeholder
- **sbservertag**: `":$0 %n(%cchange with ^X%n)"` → Colors: `%n`, `%c`
- **sbmore**: `"  %r<%R<%k< %nmore %k>%R>%r>  "` → Colors: `%r`, `%R`, `%k`, `%n`
- **sblag**: `"{sb Lagging %r$0-%K seconds!}"` → Colors: `%r`, `%K`
- **sb_default_bg**: `"%4"` → Background color
- **sb_act_sep**: `"%K/"` → Color: `%K`, Separator: `/`
- **sb_act_text**: `"%R$*"` → Color: `%R` (red)
- **sb_act_msg**: `"%G$*"` → Color: `%G` (green)
- **sb_act_hilight**: `"%M$*"` → Color: `%M` (magenta)
- **sb_act_hilight_color**: `"%B$0$1-%n"` → Colors: `%B`, `%n`

#### Status Bar Backgrounds
- **sb_info1_bg**: `"%G"` → Color: `%G` (green)
- **sb_window_bg**: `"%B%W"` → Colors: `%B`, `%W`
- **sb_window2_bg**: `"%B%W"` → Colors: `%B`, `%W`
- **sb_info2_bg**: `"%G"` → Color: `%G` (green)

#### User Count Elements
- **sb_usercount**: `"{sb %WN%wetwork:%n %K$tag }{sb %WU%wsers: %R$0 %K$1-"` → Multiple colors and placeholders
- **sb_uc_normal**: `"%WN%wormal %Y$*%R]"` → Colors: `%W`, `%w`, `%Y`, `%R`
- **sb_uc_ops**: `"%R[%WIRC%wOpers %_%y$mh_opercount  ➜  %WO%wpers %_%Y$*%c  ➜ "` → Complex with icon: `➜`
- **sb_uc_voices**: `"%WV%woice %Y$*%c  ➜ "` → Colors and icon: `➜`

#### NM2 Script Elements
- **nickalign**: `""` → Empty (nm2-specific)
- **nickcolor**: `"%n"` → Color: `%n` (nm2-specific)
- **nicktrunc**: `""` → Empty (nm2-specific)
- **cumode_space**: `" "` → Space character (nm2-specific)

---

## 3. FORMATS Section (Lines 140-225)

### fe-common/core (Lines 141-170)

#### Join/Part/Kick/Quit Events
- **join**: `"%Kmode   %Wjoin  %K>%G>%W> %n{channick_hilight %W$0} {chanhost_hilight %w$1}"`
  - Colors: `%K`, `%W`, `%G`, `%n`, `%w`
  - Icons: `>` (progression arrows)
  - Placeholders: `$0`, `$1`

- **part**: `"%Kmode   %wpart   %W<%r<%R< %n{channick_hilight2 %w$0} {chanhost_hilight %K$1} %W{reason $3}"`
  - Colors: `%K`, `%w`, `%W`, `%r`, `%R`, `%n`
  - Icons: `<` (regression arrows)
  - Placeholders: `$0`, `$1`, `$3`

- **kick**: `"%Kmode   %RKICK   %R<%r<%K< %n{channick_hilight2 %W$0} by {nick $2} %W{reason $3}"`
  - Colors: `%K`, `%R`, `%r`, `%n`, `%W`
  - Icons: `<` (regression arrows)
  - Placeholders: `$0`, `$2`, `$3`

- **quit**: `"%Kmode  %RKILLED %R<<< %n{channick_hilight2 %w$0}%n {chanhost_hilight %K$1}%n %W{reason $2}"`
  - Colors: `%K`, `%R`, `%n`, `%w`
  - Icons: `<<<` (triple arrows)
  - Placeholders: `$0`, `$1`, `$2`

#### Notices & Information
- **notice**: `"%GNotice %K>%W>%B> %n{nick $0}: $1"`
  - Colors: `%G`, `%K`, `%W`, `%B`, `%n`
  - Icons: `>` (progression arrows)
  - Placeholders: `$0`, `$1`

- **servertag**: `"%b[%W$0%b] "`
  - Colors: `%b`, `%W`
  - Placeholder: `$0`

- **invite**: `"%GInvites %K>%W>%B> %n{nick $0} to {channel $1}"`
  - Colors: `%G`, `%K`, `%W`, `%B`, `%n`
  - Icons: `>` (progression arrows)
  - Placeholders: `$0`, `$1`

#### Topic Management
- **new_topic**: `"%Kmode   %R#      %K>%W>%B> %nTopic was changed by %W{channick $0} in {channel $1} to: $2"`
  - Colors: `%K`, `%R`, `%W`, `%B`, `%n`
  - Icons: `#`, `>` (progression arrows)
  - Placeholders: `$0`, `$1`, `$2`

- **topic_unset**: `"%Kmode   %R#   %R<%W<%B< %nTopic for %n{channel $1} %Wunset by %n{channick $0}"`
  - Colors: `%K`, `%R`, `%W`, `%B`, `%n`
  - Icons: `#`, `<` (regression arrows)
  - Placeholders: `$0`, `$1`

#### Nick Changes
- **your_nick_changed**: `"You're Nickname %nis now %W{nick $1} ☣ "`
  - Colors: `%n`, `%W`
  - Icon: `☣` (biohazard symbol)
  - Placeholder: `$1`

- **nick_changed**: `"%Kmode   ~   {channick %W$0} %wis now %W{%Wchannick_hilight %W$1}"`
  - Colors: `%K`, `%W`, `%w`
  - Icon: `~` (tilde)
  - Placeholders: `$0`, `$1`

#### Channel Information
- **talking_in**: `"%G You are now talking in {%Rc%Whannel $0}"`
  - Colors: `%G`, `%R`, `%W`
  - Placeholder: `$0`

- **not_in_channels**: `"%R You are not on any %Rc%Whannels"`
  - Colors: `%R`, `%W`

- **names**: `"{names_users %W ➜ Users ☣ ) (channel {names_channel $0}} ➜ (%G$1) ☣ "`
  - Colors: `%W`, `%G`
  - Icons: `➜`, `☣`
  - Placeholders: `$0`, `$1`

- **endofnames**: `"{channel %w$0}: %WTotal of {hilight %B((%G$1%R))%W} %wnicks%n {comment %WOps {hilight %W((%G$2%W))%W}, Voice %B(({hilight %G$4%R))%W}, Normal %B(({hilight %G$5%R))%W}"`
  - Complex format with multiple colors and nested parentheses
  - Placeholders: `$0`, `$1`, `$2`, `$4`, `$5`

#### Message Formats (NM2-Compatible)
- **pubmsg**: `"{pubmsgnick $nickalign$2 {pubnick $nickcolor$0$nicktrunc}}$1"`
  - NM2 variables: `$nickalign`, `$nickcolor`, `$nicktrunc`
  - Placeholders: `$0`, `$1`, `$2`

- **own_msg**: `"{ownmsgnick $nickalign$2 {ownnick $nickcolor$0$nicktrunc}}$1"`
  - NM2 variables: `$nickalign`, `$nickcolor`, `$nicktrunc`
  - Placeholders: `$0`, `$1`, `$2`

- **own_msg_channel**: `"{ownmsgnick $nickalign$3 {ownnick $nickcolor$0$nicktrunc}{msgchannel $1}}$2"`
  - NM2 variables: `$nickalign`, `$nickcolor`, `$nicktrunc`
  - Placeholders: `$0`, `$1`, `$2`, `$3`

- **pubmsg_me**: `"{pubmsgmenick $nickalign$2 {menick $0$nicktrunc}}$1"`
  - NM2 variables: `$nickalign`, `$nicktrunc`
  - Placeholders: `$0`, `$1`, `$2`

- **pubmsg_me_channel**: `"{pubmsgmenick $nickalign$3 {menick $0$nicktrunc}{msgchannel $1}}$2"`
  - NM2 variables: `$nickalign`, `$nicktrunc`
  - Placeholders: `$0`, `$1`, `$2`, `$3`

- **pubmsg_hilight**: `"{pubmsghinick $0 $nickalign$3 $1$nicktrunc}$2"`
  - NM2 variables: `$nickalign`, `$nicktrunc`
  - Placeholders: `$0`, `$1`, `$2`, `$3`

- **pubmsg_hilight_channel**: `"{pubmsghinick $0 $nickalign$4 $1$nicktrunc{msgchannel $2}}$3"`
  - NM2 variables: `$nickalign`, `$nicktrunc`
  - Placeholders: `$0`, `$1`, `$2`, `$3`, `$4`

- **pubmsg_channel**: `"{pubmsgnick $nickalign$3 {pubnick $nickcolor$0$nicktrunc}{msgchannel $1}}$2"`
  - NM2 variables: `$nickalign`, `$nickcolor`, `$nicktrunc`
  - Placeholders: `$0`, `$1`, `$2`, `$3`

- **own_msg_private_query**: `"{ownprivmsgnick $nickalign{ownprivnick $nickcolor$2$nicktrunc}}$1"`
  - NM2 variables: `$nickalign`, `$nickcolor`, `$nicktrunc`
  - Placeholders: `$1`, `$2`

- **msg_private_query**: `"{privmsgnick $nickalign$nickcolor$0$nicktrunc}$2"`
  - NM2 variables: `$nickalign`, `$nickcolor`, `$nicktrunc`
  - Placeholders: `$0`, `$2`

### fe-common/irc (Lines 171-199)

#### IRC-Specific Events
- **inviting**: `"%K>%G>%W>%n Inviting {nick $0} to {channel $1}"`
  - Colors: `%K`, `%G`, `%W`, `%n`
  - Icons: `>` (progression arrows)
  - Placeholders: `$0`, `$1`

- **topic_info**: `"%Kmode   %nTopic set by %N{channick $0} {comment $1}"`
  - Colors: `%K`, `%n`, `%N`
  - Placeholders: `$0`, `$1`

- **server_chanmode_change**: `"{netsplit %CServerMode%n}/{channelhilight $0} {mode $1} by {nick $2}"`
  - Colors: `%C`, `%n`
  - Placeholders: `$0`, `$1`, `$2`

#### WHOIS Information
- **whois**: `"{nick $0} ({nickhost $1%R@%n$2}) {comment $4}%:{whois %_i%_rcname $3}"`
  - Colors: `%R`, `%n`, Formatting: `%_`
  - Placeholders: `$0`, `$1`, `$2`, `$3`, `$4`

- **whowas**: `"           %WWHOWAS%K       %n%:%r*%n {nick $0} ({nickhost $1%R@%n$2})%:{whois %_i%_rcname $3}"`
  - Colors: `%W`, `%K`, `%n`, `%r`, `%R`, Formatting: `%_`
  - Icon: `*`
  - Placeholders: `$0`, `$1`, `$2`, `$3`

- **whois_idle**: `"{whois3 %_i%_dle %|$1 days $2 hours $3 mins $4 secs}"`
  - Formatting: `%_`, `%|`
  - Placeholders: `$1`, `$2`, `$3`, `$4`

- **whois_idle_signon**: `"{whois3 %_i%_dle %|$1 days $2 hours $3 mins $4 secs (signon: $5)}"`
  - Formatting: `%_`, `%|`
  - Placeholders: `$1`, `$2`, `$3`, `$4`, `$5`

- **whois_server**: `"{whois2 %_s%_erver %|$1 ($2)}"`
  - Formatting: `%_`, `%|`
  - Placeholders: `$1`, `$2`

- **whois_oper**: `"{whois3 %_o%_perator {hilight $1}}"`
  - Formatting: `%_`
  - Placeholder: `$1`

- **whois_registered**: `"{whois %_a%_uth has registered this nick}"`
  - Formatting: `%_`

- **whois_help**: `"{whois %_d%_uty is available for help}"`
  - Formatting: `%_`

- **whois_modes**: `" {whois %_m%_odes $1}"`
  - Formatting: `%_`
  - Placeholder: `$1`

- **whois_realhost**: `"{whois %_h%_ostname $1-}"`
  - Formatting: `%_`
  - Placeholder: `$1-`

- **whois_usermode**: `"{whois %_u%_sermode $1}"`
  - Formatting: `%_`
  - Placeholder: `$1`

- **whois_channels**: `"{whois %_c%_hannels %|$1}"`
  - Formatting: `%_`, `%|`
  - Placeholder: `$1`

- **whois_away**: `"{whois %_a%_way %W%|$1}"`
  - Colors: `%W`, Formatting: `%_`, `%|`
  - Placeholder: `$1`

- **whois_special**: `"{whois  %|{hilight $1}}"`
  - Formatting: `%|`
  - Placeholder: `$1`

- **end_of_whois**: `"%WEnd of WHOIS ☣ "`
  - Color: `%W`
  - Icon: `☣`

- **end_of_whowas**: `"%wEnd of WHOWAS ☣ "`
  - Color: `%w`
  - Icon: `☣`

- **whois_not_found**: `"%wThere is no such Nick:%R.%w {nick %W$0}"`
  - Colors: `%w`, `%R`, `%W`
  - Placeholder: `$0`

- **who**: `"{channelhilight %C$[!10]0%n} %|{nick $[!9]1} %c$[!3]2%n $[!2]3 $4%R@%n$5 :: $6"`
  - Colors: `%C`, `%n`, `%c`, `%R`
  - Format specifiers: `$[!10]0`, `$[!9]1`, `$[!3]2`, `$[!2]3`
  - Placeholders: `$4`, `$5`, `$6`

#### CTCP & Actions
- **ctcp_requested**: `"requested $2 from {nick $3} {ctcp {hilight $0} {comment $1}}"`
  - Placeholders: `$0`, `$1`, `$2`, `$3`

- **own_action**: `"$nickalign{ownaction $0$nicktrunc}$1"`
  - NM2 variables: `$nickalign`, `$nicktrunc`
  - Placeholders: `$0`, `$1`

- **action_private**: `"$nickalign{pvtaction $0$nicktrunc}$2"`
  - NM2 variables: `$nickalign`, `$nicktrunc`
  - Placeholders: `$0`, `$2`

- **action_private_query**: `"$nickalign{pvtaction_query $0$nicktrunc}$2"`
  - NM2 variables: `$nickalign`, `$nicktrunc`
  - Placeholders: `$0`, `$2`

- **action_public**: `"$nickalign{pubaction $0$nicktrunc}$1"`
  - NM2 variables: `$nickalign`, `$nicktrunc`
  - Placeholders: `$0`, `$1`

### fe-common/irc/dcc (Lines 200-212)

#### DCC Message Formats
- **own_dcc**: `"{dccownmsg {dccownnick $1}}$2"`
  - Placeholders: `$1`, `$2`

- **own_dcc_action**: `"{dccownaction_target $0 $1}$2"`
  - Placeholders: `$0`, `$1`, `$2`

- **own_dcc_action_query**: `"{dccownaction $0}$2"`
  - Placeholders: `$0`, `$2`

- **own_dcc_ctcp**: `"{ownctcp ctcp $0}$1 $2"`
  - Placeholders: `$0`, `$1`, `$2`

- **dcc_msg**: `"{dccmsg $0}$1"`
  - Placeholders: `$0`, `$1`

- **action_dcc**: `"{dccaction $0}$1"`
  - Placeholders: `$0`, `$1`

- **action_dcc_query**: `"{dccaction $0}$1"`
  - Placeholders: `$0`, `$1`

- **own_dcc_query**: `"{dccownmsg {dccownnick $0}}$2"`
  - Placeholders: `$0`, `$2`

- **dcc_msg_query**: `"{dccmsg $0}$1"`
  - Placeholders: `$0`, `$1`

- **dcc_ctcp**: `"{dcc %W>>>DCC CTCP {hilight $1} received from {hilight $0}: $2}"`
  - Color: `%W`
  - Icons: `>>>` 
  - Placeholders: `$0`, `$1`, `$2`

### nm2 Script Block (Line 213)
```irc
nm2 = { neat_pad_char = " "; };
```
- **neat_pad_char**: Space character for padding

### AWL (Advanced Window List) Block (Lines 214-223)
```irc
"Irssi::Script::adv_windowlist" = {
  awl_display_key_active = "%0%B$N %B${cumode_space}%y$H$C$S";
  awl_display_key = "%B$N %y${cumode_space}%B$H$C$S";
  awl_viewer_item_bg = "%N";
  awl_display_header = "";
  awl_display_nokey = "%B$N %y${cumode_space}%B$H$C$S";
  awl_display_nokey_visible = "%B$N %M${cumode_space}%M$H%N$C$S";
  awl_display_key_visible = "%B$N %M${cumode_space}%M$H%N$C$S";
  awl_display_nokey_active = "%0%B$N %B${cumode_space}%y$H$C$S";
};
```

#### AWL Format Analysis:
- **Colors**: `%0` (background), `%B` (blue), `%y` (yellow), `%M` (magenta), `%N` (normal)
- **Placeholders**: 
  - `$N` - Window number
  - `$H` - Window hilight status
  - `$C` - Window content indicator 
  - `$S` - Window status
  - `${cumode_space}` - Channel user mode space (from abstracts)

### nm2 Script Block (Line 224)
```irc
"Irssi::Script::nm2" = { neat_pad_char = " "; };
```
- Duplicate nm2 configuration with space padding

---

## 4. COMPLETE PLACEHOLDER CATALOG

### Standard Irssi Placeholders:
- `$*` - All arguments
- `$0` - First argument
- `$1` - Second argument  
- `$1-` - Second argument through end
- `$2` - Third argument
- `$2-` - Third argument through end
- `$3` - Fourth argument
- `$4` - Fifth argument
- `$5` - Sixth argument
- `$6` - Seventh argument
- `$7` - Eighth argument
- `$8-` - Eighth argument through end

### Format Specifiers:
- `$[12]0` - First argument, 12 character width
- `$[!10]0` - First argument, right-aligned 10 characters
- `$[!9]1` - Second argument, right-aligned 9 characters
- `$[!3]2` - Third argument, right-aligned 3 characters
- `$[!2]3` - Fourth argument, right-aligned 2 characters
- `$[9]1-` - Arguments 1+, 9 character width

### nm2-Specific Variables:
- `$nickalign` - Nick alignment (empty in this theme)
- `$nickcolor` - Nick color (set to `%n`)
- `$nicktrunc` - Nick truncation (empty in this theme)
- `${cumode_space}` - Channel user mode space

### AWL-Specific Placeholders:
- `$N` - Window number
- `$H` - Window hilight status
- `$C` - Window content indicator
- `$S` - Window status

### Special Variables:
- `$A` - Away message (commented out)
- `$tag` - Server tag
- `$mh_opercount` - IRC operator count (mh script)
- `$C` - Channel (in channel2 abstract)

---

## 5. COMPLETE COLOR CODE CATALOG

### Basic Colors:
- `%k` - Black
- `%K` - Dark gray
- `%r` - Dark red
- `%R` - Red
- `%g` - Dark green
- `%G` - Green
- `%y` - Dark yellow/brown
- `%Y` - Yellow
- `%b` - Dark blue
- `%B` - Blue
- `%m` - Dark magenta
- `%M` - Magenta
- `%p` - Dark cyan
- `%P` - Cyan (alternative)
- `%c` - Cyan
- `%C` - Light cyan
- `%w` - Light gray
- `%W` - White
- `%n` - Default/normal
- `%N` - Normal/reset

### Background Colors:
- `%0` - Black background
- `%4` - Blue background

### Text Formatting:
- `%_` - Underline toggle
- `%|` - Indent/line continuation

---

## 6. COMPLETE ICON CATALOG

### Arrow Icons:
- `➜` - Right arrow (used in line_start, timestamp, notice, sb, names, sb_uc_ops, sb_uc_voices)
- `>` - Greater than (progression arrows in join, notice, invite, new_topic, inviting)
- `<` - Less than (regression arrows in part, kick, topic_unset)
- `<<<` - Triple less than (quit)
- `>>>` - Triple greater than (dcc_ctcp)
- `->` - Dash arrow (pvtaction, dccownaction)

### Symbol Icons:
- `⎢` - Box drawing (timestamp)
- `☣` - Biohazard symbol (sb, your_nick_changed, names, end_of_whois, end_of_whowas)
- `*` - Asterisk (action, wallop_action, pvtaction_query, whowas)
- `~` - Tilde (nick_changed)
- `#` - Hash (new_topic, topic_unset)
- `:` - Colon (various message formats)
- `/` - Slash (sb_act_sep)

### Brackets & Parentheses:
- `[]` - Square brackets (multiple formats)
- `()` - Parentheses (multiple formats)
- `<>` - Angle brackets (multiple formats)

---

## 7. CRITICAL PRESERVATION REQUIREMENTS

### Must Preserve:
1. **All nm2 variables**: `$nickalign`, `$nickcolor`, `$nicktrunc`, `${cumode_space}`
2. **All AWL placeholders**: `$N`, `$H`, `$C`, `$S`
3. **All unique icons**: `➜`, `⎢`, `☣`, `>`, `<`, `*`, `~`, `#`, `->`
4. **All format specifiers**: `$[12]0`, `$[!10]0`, etc.
5. **All abstract references**: `{msgnick}`, `{channick}`, etc.
6. **All color transitions**: Exact color codes and their sequences
7. **All spacing**: Critical for alignment and readability

### Theme Identity Elements:
1. **Green arrows** (`%G➜`) - Core visual identity
2. **Biohazard symbols** (`☣`) - Unique tahio branding
3. **Box drawing** (`⎢`) - Distinctive timestamp formatting
4. **Progression/regression arrows** (`>`, `<`) - Event visualization
5. **Mode prefixes** (`%Kmode`) - Consistent event labeling

This audit provides a complete baseline for preserving the tahio.theme's functionality while allowing for safe color scheme modifications.
