--- <PERSON><PERSON> opened Fri Aug 08 18:05:36 2025
18:05 ➜ mode   join  >>> themeTEST <~y@*************>
18:05 ➜ Irssi: #tahio: Total of ((5)) nicks [Ops ((3)), Voice ((0)), Normal ((2))]
18:05  themeTEST:ad
18:05  themeTEST:asd
18:05 ➜ Irssi: Join to #tahio was synced in 10 secs
--- <PERSON>g closed Fri Aug 08 18:10:33 2025
--- <PERSON><PERSON> opened Fri Aug 08 18:11:26 2025
18:11 ➜ mode   join  >>> themeTEST <~y@*************>
18:11 ➜ Irssi: #tahio: Total of ((5)) nicks [Ops ((3)), Voice ((0)), Normal ((2))]
18:11 ➜ Irssi: Join to #tahio was synced in 8 secs
18:13      @yooz:o
18:13      @yooz:[;
18:13      @yooz:powiem ci ze lepsze rozwiazanie dla irssi to miec 2 oddzielne okna na 1 tmuxie dla kazdej sieci
18:13      @yooz:o<PERSON><PERSON><PERSON><PERSON><PERSON> wiaze sie to z przelaczaniem
18:14      @yooz:<PERSON><PERSON> w irssi nie bedzie poprawnie to dzialalo w sensie 2 sieci 
18:14      @yooz:bo nawet jak chchialem dodac kolejny kanal dla sieci ircnet to pokazywal on mi sie na samym dole listy
18:20      @yooz:jak zrobisz theme to wracam do irssi :)
18:20      @yooz:na 2 oknach bede jechal
18:20      @yooz:;]
18:37  : hm
--- Log closed Fri Aug 08 18:37:47 2025
--- Log opened Fri Aug 08 18:37:47 2025
18:37 ♡ ✨ themeTEST  <~y@*************>
18:37 ♡ Irssi: Total of 5 nicks (3 ops, 0 voices)
18:37 ♡ Irssi: Join to #tahio was synced in 8 secs
18:40  : lol
18:41 @: lol
19:59  : asd
--- Log closed Fri Aug 08 19:59:43 2025
--- Log opened Fri Aug 08 21:28:03 2025
21:28 · ⊕ themeTEST  [~y@*************]
21:28 · Irssi: #tahio: Total of ((5)) nicks (Ops ((3)), Voice ((0)), Normal ((2)))
21:28 · Irssi: Join to #tahio was synced in 8 secs
--- Log closed Fri Aug 08 21:28:18 2025
--- Log opened Fri Aug 08 21:28:18 2025
21:28 · ⊕ themeTEST  [~y@*************]
21:28 · Irssi: #tahio: Total of ((5)) nicks (Ops ((3)), Voice ((0)), Normal ((2)))
21:28 · Irssi: Join to #tahio was synced in 8 secs
--- Log closed Fri Aug 08 21:28:54 2025
--- Log opened Fri Aug 08 21:31:44 2025
21:31 → bughunter <~y@*************>
21:31 Irssi: #tahio: Total of 5 nicks [3 ops, 0 halfops, 0 voices, 2 normal]
--- Log closed Fri Aug 08 21:31:47 2025
--- Log opened Fri Aug 08 21:31:47 2025
21:31 → bughunter <~y@*************>
21:31 Irssi: #tahio: Total of 5 nicks [3 ops, 0 halfops, 0 voices, 2 normal]
21:31 Irssi: Join to #tahio was synced in 10 secs
21:32  bughunter │ wqdas
21:32  bughunter │ dasd
21:32  bughunter │ .die
21:33 → gypigypi <~<EMAIL>>
21:33 → outhector <~<EMAIL>>
21:33 → kikit31t <~<EMAIL>>
21:33 → kissme13 <~<EMAIL>>
21:33 → Pippapoo1 <~<EMAIL>>
21:33 → cookshop <~LUYQff@************>
21:33 → bnt <~<EMAIL>>
21:33 → lidka12 <~<EMAIL>>
21:33 → schwepe <~<EMAIL>>
21:33 → trimmed <~<EMAIL>>
21:33 → sox2005 <~<EMAIL>>
21:33 → paledness <~<EMAIL>>
21:33 → sebbe8715 <~<EMAIL>>
21:33 → yangxl <~<EMAIL>>
21:33 → redback83 <~<EMAIL>>
21:40 ← kofany <<EMAIL>> (WeeChat 4.6.0 )
21:40 → kofany <<EMAIL>>
21:40 mode/#tahio <+o kofany> by rpi4
21:40      @yooz │ 4.6.0 ?
21:40      @yooz │ Robiel aktualizacje do 4.7.0
21:40      @yooz │ ;]
21:40      @yooz │ juz dawno robiles restar weechata ;]
21:41    @kofany │ no ogólnie fajny patent szukamn czy zjebalem w pnb
21:41      @yooz │ a zjebales ?
21:41      @yooz │ w ktorym miejscu ? :)
21:41    @kofany │ czy w czy w go-ircebvo
21:42      @yooz │ a tego nie znam
21:42      @yooz │ [;
21:42    @kofany │ no wiesz boty mysla "Wszystkie" ze maja nic ten co ostania osoba co zrobile join 
21:42    @kofany │ :S
21:42    @kofany │ yooz: wejdz n tahion 
21:42    @kofany │ i daj .abots
21:42    @kofany │ .abots
21:43      @yooz │ konekted i twoj nick
21:43    @kofany │ zrob teraz cycle
21:43    @kofany │ :>
21:43    @kofany │ tutaj
21:44    @kofany │ soba
21:44    @kofany │ nie botami
21:45    @kofany │ yooz: ?
21:48 ← yooz <<EMAIL>> ( )
21:48 → yooz <<EMAIL>>
21:48 mode/#tahio <+o yooz> by rpi4
21:48    @kofany │ i daj teraz .bots
21:48    @kofany │ .abots
21:48    @kofany │ sorki 
21:48      @yooz │ tutaj ?
21:48      @yooz │ ok
21:48      @yooz │ widze teraz yooz konekted
21:56 mode/#tahio <+o bnt> by kofany
21:57  * kofanyds
22:00      @yooz │ sd
22:00 mode/#tahio <+l 99> by kofany
22:01      @yooz │ !protekt+
22:01      @yooz │ [;
22:01 × schwepe by kofany (no reason )
22:01 × trimmed by yooz (no reason )
22:01 × Pippapoo1 by kofany (no reason )
22:01 × sebbe8715 by yooz (no reason )
22:01      @yooz │ to jak strzelanie do kaczek :)
22:01    @kofany │ nie kop
22:01      @yooz │ ok
22:01    @kofany │ bo chce sprawdzic czy zmienia nicka po kicky
22:02    @kofany │ :D
22:02    @kofany │ a tera maja yooz
22:02    @kofany │ wiec jak ty kopniesz w miedzyczasie to sie nie dowiem
22:02      @yooz │ ok
22:02 × paledness by kofany (dupa )
22:02 × sox2005 <~<EMAIL>> (EOF From client )
22:03 ← kofany <<EMAIL>> (WeeChat 4.6.0 )
22:03 → kofany <<EMAIL>>
22:03 mode/#tahio <+o kofany> by rpi4
22:04 → schwepe <~<EMAIL>>
22:04 → trimmed <~<EMAIL>>
22:04 → Pippapoo1 <~<EMAIL>>
22:06 × schwepe <~<EMAIL>> (Connection reset by peer )
22:07 × lidka12 <~<EMAIL>> (Ping timeout )
22:09 × kikit31t <~<EMAIL>> (EOF From client )
22:09 × Pippapoo1 <~<EMAIL>> (EOF From client )
22:13 × gypigypi <~<EMAIL>> (EOF From client )
22:13 × outhector <~<EMAIL>> (EOF From client )
22:13 × yangxl <~<EMAIL>> (EOF From client )
22:13 × redback83 <~<EMAIL>> (EOF From client )
22:13 × cookshop <~LUYQff@************> (EOF From client )
22:13 × bnt <~<EMAIL>> (EOF From client )
22:13 × kissme13 <~<EMAIL>> (EOF From client )
22:13 × trimmed <~<EMAIL>> (EOF From client )
22:32 × tahioN <~tahio@2a02:2454:8577:d300:ed9c:aa92:3fcb:6d30> (EOF From client )
23:26      @yooz │ o bughunter zostal
--- Day changed Sat Aug 09 2025
00:55 → tahioN <~tahio@2a02:2454:8577:d300:ed9c:aa92:3fcb:6d30>
00:56 ← kofany <<EMAIL>> (WeeChat 4.6.0 )
00:56 → kofany <<EMAIL>>
00:56 mode/#tahio <+o kofany> by rpi4
--- Log closed Sat Aug 09 01:46:50 2025
--- Log opened Sat Aug 09 01:46:51 2025
01:46 ▸ ◈ → bughunter ⟨~y@*************⟩
01:46 ▸ Irssi: #tahio: Total of ⟨5⟩ nicks ⟨Ops ⟨3⟩, Voice ⟨0⟩, Normal ⟨2⟩⟩
01:46 ▸ Irssi: Join to #tahio was synced in 8 secs
--- Log closed Sat Aug 09 01:47:21 2025
