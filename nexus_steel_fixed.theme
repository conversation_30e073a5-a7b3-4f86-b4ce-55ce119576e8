# Nexus Steel v1.0 - Fixed
# Modern cyberpunk IRC theme for the 21st century terminal experience
# Based on Irssi color documentation and tahio.theme structure

################################################################################
# NEXUS STEEL COLOR PALETTE LEGEND
################################################################################
#
# Irssi Color Codes used:
# %K/%k - black/dark gray    %R/%r - red/dark red
# %G/%g - green/dark green   %Y/%y - yellow/brown
# %B/%b - blue/dark blue     %M/%m - magenta/dark magenta
# %C/%c - cyan/dark cyan     %W/%w - white/gray
# %N/%n - reset to default   %_ - underline
#
# Nexus Steel Palette Mapping:
# %K - void-black (shadows)      %R - crimson-red (errors)
# %G - neon-green (success)      %Y - amber-yellow (warnings)
# %B - electric-blue (primary)   %M - plasma-purple (special)
# %C - cyan-steel (info)         %W - silver-white (text)
# %k - shadow-gray (disabled)    %r - hot-red (highlights)
# %g - lime-green (highlights)   %y - gold-yellow (highlights)
# %b - sky-blue (highlights)     %m - violet-purple (highlights)
# %c - aqua-cyan (data)          %w - light-gray (secondary)
#
# UTF-8 Icons Used:
# ⚡ ▶ ◀ ◆ ⚠ ⬢ ∞ ⧐ ⚙ ⟨ ⟩ ▸
#
################################################################################

replaces = { ":()=" = "%K$*%n"; };

abstracts = {
  # Core visual elements with modern cyberpunk styling
  line_start = "%B▸%n ";
  timestamp = "%C⟨ %b$*%C ⟩%n";
  hilight = "%g$*%n";
  error = "%R⚠ $*%n";
  channel = "%b%_$*%_%n";
  channel2 = "%B$C%n";
  nick = "%n$*%n";
  nickhost = "$*";
  server = "%G$*%n";
  comment = "%k⟨%n$*%k⟩%n";
  reason = "%w(%n$*%w)%n";
  mode = "%C⟨%B%_$*%n%_%C⟩%n";

  # Channel nick highlights with steel palette
  channick_hilight = "%b$*%n";
  channick_hilight2 = "%g$*%n";
  chanhost_hilight = "%k⟨{nickhost $*}⟩%k";
  channick = "%w$*%n";
  chanhost = "%k⟨%w{nickhost $*}%k⟩%n";
  channelhilight = "%B$*%n";
  ban = "$*";

  # Message styling with clean separators
  msgnick = "%w$0%k$1-%B▸%n%|";
  ownmsgnick = "%g$0%k$1-%B▸%n%|";
  ownnick = "%g%_$*%_%n";
  pubmsgnick = "{msgnick $0 $1-}";
  pubnick = "%_%_$*%_%n";
  pubmsgmenick = "{msgnick $0 %_$1-%_%n}";
  menick = "%b$*%n";
  pubmsghinick = "{msgnick $1 $0$2-}";
  msgchannel = "%B▸%n%_$*%_";

  # Private messages with modern brackets
  privmsg = "%C⟨%b$0%C⟩%k⟨{nickhost $1-}⟩%n ";
  ownprivmsg = "%C⟨%g$0%C⟩%k⟨$1-⟩%n ";
  ownprivmsgnick = "%C⟨%g$*%C⟩%n ";
  ownprivnick = "%_%_$*%_%n";
  privmsgnick = "%C⟨%b$*%C⟩%n ";

  # Actions with modern styling
  action = "%k◦%n";
  ownaction = "{action} %g$0 $1-";
  pvtaction = "%C⟨%Bquery%C⟩%k⟨$0⟩%n {action} %B→%n $1-";
  pvtaction_query = "%k◦ $* {action} ";
  pubaction = "{action} %b$* %k◦%n";

  # WHOIS information with steel styling
  whois = "$[12]0 : $1-";
  whois1 = "$[12]0 : $1-";
  whois2 = "$[12]0 : $1-";
  whois3 = "$[12]0 : $1-";

  # Notices with modern design
  ownnotice = "%C⟨%Bnotice%C⟩%k⟨$1-⟩%n ";
  notice = "%m◆%n %mNOTICE%n %B▶%b▶%g▶%n {nick $0}: $1";
  pubnotice_channel = ":$*";
  pvtnotice_host = "%k⟨$*⟩%n";
  servernotice = "%G⬢%n %GSERVER%n %B▶%n $0";

  # CTCP with clean styling
  ownctcp = "%M⟨%n$0%M⟩%C⟨%n$1-%C⟩%n ";
  ctcp = "%M⧐%n %MCTCP%n %B▶%b▶%g▶%n {nick $0} %g$1%n $2 %b$3%n $4 $5 %g$6%n";

  # Wallops
  wallop = " $*: ";
  wallop_nick = " $*";
  wallop_action = " ◦ $* ";

  # Network events with modern colors
  netsplit = "%R$*";
  netjoin = "%G$*";

  # Names list styling
  names_nick = "%b$0%k$[9]1-%n ";
  names_users = "(%C$0%k(%b$1%k))";
  names_channel = "%B$*";

  # DCC transfers
  dcc = "%b$0%n $1 $3 $4 %k$5 $6 $7 $8-%n";
  dccfile = "%_%_$*%_%n";
  dccownmsg = "%C⟨%Bdcc%C⟩%k⟨$*⟩%n ";
  dccownnick = "%C$*%n";
  dccownaction = "%C⟨%Bdcc%C⟩%k⟨$0⟩%n {action} %B→%n $1-";
  dccmsg = "%C⟨%Bdcc%C⟩%k⟨$*⟩%n ";
  dccquerynick = "%b$*%n";
  dccaction = "%C⟨%Bdcc%C⟩%k⟨$*⟩%n {action}%n %|";

  # Status bar with electric styling
  sb_background = "%K";
  sb_topic_bg = "%B%W";
  sb = "%B▸%n $0-%B ◦%n";
  prompt = "%C⟨%b$0%C⟩%n ";
  sbmode = "$0-";
  sbservertag = ":$0 %n(%Gchange with ^X%n)";
  sbmore = " %R◀%r◀%k◀ %nmore %k▶%r▶%R▶ ";
  sblag = "%R⚠%n %RLAG%n %w$0-%n seconds";
  sb_default_bg = "%K";
  sb_act_sep = "%k/%n";
  sb_act_text = "%w$*";
  sb_act_msg = "%b$*";
  sb_act_hilight = "%g$*";
  sb_act_hilight_color = "%b$0$1-%n";
  sb_info1_bg = "%B";
  sb_window_bg = "%B%W";
  sb_window2_bg = "%B%W";
  sb_info2_bg = "%C";

  # User count with modern styling
  sb_usercount = "{sb %BN%Getwork:%n %G$tag }{sb %BU%Gsers: %b$0 %G$1-";
  sb_uc_normal = "%BN%Gormal %g$*%b]";
  sb_uc_ops = "%b[%BIRC%GOpers %Y$mh_opercount %B▸ %BO%Gpers %g$*%B ▸ ";
  sb_uc_voices = "%BV%Goice %g$*%B ▸ ";

  # nm2 and alignment settings
  nickalign = "";
  nickcolor = "%n";
  nicktrunc = "";
  cumode_space = " ";
};

################################################################################
# EVENT FORMATS - Redesigned IRC event handling
################################################################################

formats = {
  "fe-common/core" = {
    # Modern join/part/kick/quit with UTF-8 symbols
    join = "%B▶%n %GCONNECTION%n {channick_hilight %b$0} {chanhost_hilight %k$1}";
    part = "%Y◀%n %CDISCONNECT%n {channick_hilight2 %b$0} {chanhost_hilight %k$1} %w{reason $3}";
    kick = "%R⚠%n %REXPELLED%n {channick_hilight2 %b$0} by {nick %m$2} %w{reason $3}";
    quit = "%R⏻%n %RTERMINATED%n {channick_hilight2 %b$0} {chanhost_hilight %k$1} %w{reason $2}";
    notice = "{notice $0 $1}";
    quit_once = "{channel $3} {channick $0} {chanhost $1} {reason $2}";
    servertag = "%b⟨%B$0%b⟩%n ";
    invite = "%G✉%n {nick %b$0} to {channel $1}";
    new_topic = "%B⚙%n %BTOPIC%n changed by {channick %b$0} in {channel $1}: %n$2";
    topic_unset = "%k⚙%n %kTOPIC%n cleared by {channick %b$0} in {channel $1}";
    your_nick_changed = "%M◆%n You are now %g{nick $1} ◦ ";
    nick_changed = "%M◆%n %MIDENTITY%n {channick %b$0} %w→%n {channick_hilight %g$1}";
    talking_in = "%G◈%n You are now talking in {channel $0}";
    not_in_channels = "%R◈%n You are not on any channels";
    names = "{names_users %B◦ Users ◦%n} {channel {names_channel $0}} %B▸%n (%b$1) ◦ ";
    names_nick = "{names_nick $0 $1}";
    endofnames = "{channel %G$0}: %BTotal of {hilight %b⟨%B$1%b⟩%B} %Gnicks%n {comment %BOps {hilight %b⟨%B$2%b⟩%B}, Voice %b⟨{hilight %B$4%b}⟩%B, Normal %b⟨{hilight %B$5%b}⟩%B}";

    # Away status with modern indicators
    away = "%Y◯%n %YAWAY%n %wStatus set: %n$0";
    unaway = "%G◎%n %GACTIVE%n %wStatus cleared";

    # Server connection events
    server_connect = "%G⚡%n %GSYSTEM%n ⟪ Connected to %B$0%n";
    server_disconnect = "%R⚡%n %RSYSTEM%n ⟫ Disconnected from %k$0%n";

    # Message formats with nm2 support
    pubmsg = "{pubmsgnick $nickalign$2 {pubnick $nickcolor$0$nicktrunc}}$1";
    own_msg = "{ownmsgnick $nickalign$2 {ownnick $nickcolor$0$nicktrunc}}$1";
    own_msg_channel = "{ownmsgnick $nickalign$3 {ownnick $nickcolor$0$nicktrunc}{msgchannel $1}}$2";
    pubmsg_me = "{pubmsgmenick $nickalign$2 {menick $0$nicktrunc}}$1";
    pubmsg_me_channel = "{pubmsgmenick $nickalign$3 {menick $0$nicktrunc}{msgchannel $1}}$2";
    pubmsg_hilight = "{pubmsghinick $0 $nickalign$3 $1$nicktrunc}$2";
    pubmsg_hilight_channel = "{pubmsghinick $0 $nickalign$4 $1$nicktrunc{msgchannel $2}}$3";
    pubmsg_channel = "{pubmsgnick $nickalign$3 {pubnick $nickcolor$0$nicktrunc}{msgchannel $1}}$2";
    own_msg_private_query = "{ownprivmsgnick $nickalign{ownprivnick $nickcolor$2$nicktrunc}}$1";
    msg_private_query = "{privmsgnick $nickalign$nickcolor$0$nicktrunc}$2";
  };

  "fe-common/irc" = {
    inviting = "%G✉%n Inviting {nick %b$0} to {channel $1}";
    topic_info = "%B⚙%n Topic set by %b{channick $0} {comment $1}";
    server_chanmode_change = "{netsplit %BServerMode%n}/{channelhilight $0} {mode $1} by {nick $2}";

    # Enhanced WHOIS with steel styling
    whois = "%C∞%n %CINFO%n {nick %b$0} %k({nickhost %b$1%k@%b$2}) %w$4 %k│%w {whois ircname %n$3}";
    whowas = "           %CWHOWAS%G       %n%:%b*%n {nick $0} ({nickhost $1%b@%n$2})%:{whois %_i%_rcname $3}";
    whois_idle = "%C∞%n %CIDLE%n %k│%n %w$1d %w$2h %w$3m %w$4s";
    whois_idle_signon = "%C∞%n %CIDLE%n %k│%n %w$1d %w$2h %w$3m %w$4s (signon: $5)";
    whois_server = "%C∞%n %CSERVER%n %k│%n %b$1 %k(%w$2%k)";
    whois_oper = "%C∞%n %COPER%n %k│%n {hilight %g$1}";
    whois_registered = "%C∞%n %CAUTH%n %k│%n has registered this nick";
    whois_help = "%C∞%n %CDUTY%n %k│%n is available for help";
    whois_modes = " %C∞%n %CMODES%n %k│%n $1";
    whois_realhost = "%C∞%n %CHOST%n %k│%n $1-";
    whois_usermode = "%C∞%n %CUMODE%n %k│%n $1";
    whois_channels = "%C∞%n %CCHANNELS%n %k│%n %b$1";
    whois_away = "%C∞%n %CAWAY%n %k│%n %Y$1";
    whois_special = "%C∞%n %CSPECIAL%n %k│%n {hilight %g$1}";
    end_of_whois = "%CEnd of WHOIS ◦ ";
    end_of_whowas = "%GEnd of WHOWAS ◦ ";
    whois_not_found = "%GThere is no such Nick:%R.%G {nick %B$0}";

    who = "{channelhilight %B$[!10]0%n} %|{nick $[!9]1} %B$[!3]2%n $[!2]3 $4%b@%n$5 :: $6";
    ctcp_requested = "requested $2 from {nick $3} {ctcp {hilight $0} {comment $1}}";

    # Actions with nm2 support
    own_action = "$nickalign{ownaction $0$nicktrunc}$1";
    action_private = "$nickalign{pvtaction $0$nicktrunc}$2";
    action_private_query = "$nickalign{pvtaction_query $0$nicktrunc}$2";
    action_public = "$nickalign{pubaction $0$nicktrunc}$1";
  };

  "fe-common/irc/dcc" = {
    own_dcc = "{dccownmsg {dccownnick $1}}$2";
    own_dcc_action = "{dccownaction_target $0 $1}$2";
    own_dcc_action_query = "{dccownaction $0}$2";
    own_dcc_ctcp = "{ownctcp ctcp $0}$1 $2";
    dcc_msg = "{dccmsg $0}$1";
    action_dcc = "{dccaction $0}$1";
    action_dcc_query = "{dccaction $0}$1";
    own_dcc_query = "{dccownmsg {dccownnick $0}}$2";
    dcc_msg_query = "{dccmsg $0}$1";
    dcc_ctcp = "{dcc %B▸▸▸ DCC CTCP {hilight $1} received from {hilight $0}: $2}";
  };

################################################################################
# AWL CONFIGURATION - Advanced Window List with Nexus Steel styling
################################################################################

  "Irssi::Script::adv_windowlist" = {
    # Active window with electric blue accent and power symbol
    awl_display_key_active = "%B⚡%n $N %b${cumode_space}%G$H$C$S";

    # Inactive windows with subtle steel gray styling
    awl_display_key = "  %w$N %k${cumode_space}%w$H$C$S";

    # Background styling consistent with statusbar
    awl_viewer_item_bg = "%K";

    # Clean header without decoration
    awl_display_header = "";

    # Windows without hotkeys using steel gray palette
    awl_display_nokey = "  %k$N %k${cumode_space}%w$H$C$S";

    # Visible windows with cyan accent and visibility indicator
    awl_display_nokey_visible = "%C◆%n %w$N %b${cumode_space}%C$H%n$C$S";
    awl_display_key_visible = "%C◆%n %w$N %b${cumode_space}%C$H%n$C$S";

    # Active window without hotkey using power symbol
    awl_display_nokey_active = "%B⚡%n $N %b${cumode_space}%G$H$C$S";

    # Activity level colors following Nexus Steel palette
    awl_data_level_none = "%w";     # silver-gray for no activity
    awl_data_level_low = "%b";      # sky-blue for low activity
    awl_data_level_medium = "%Y";   # amber-yellow for medium activity
    awl_data_level_high = "%R";     # crimson-red for high activity/highlights

    # Additional styling options for enhanced visual consistency
    awl_separator = "%k▸%n";
    awl_abbrev_chars = "→";
    awl_hide_empty = "0";
    awl_maxlines = "0";
    awl_sort = "refnum";
  };

################################################################################
# NM2 CONFIGURATION - Nick alignment with enhanced Nexus Steel integration
################################################################################

  "Irssi::Script::nm2" = {
    neat_pad_char = " ";
    neat_left_actions = "0";
    neat_right_actions = "1";
    neat_left_messages = "0";
    neat_right_messages = "1";
    neat_maxlength = "15";
    neat_colors = "1";
    neat_shrink_uniq = "1";
    neat_colorize = "1";
    neat_allow_shrinking = "1";
    neat_melength = "15";
  };
};

# End of Nexus Steel v1.0 Theme
