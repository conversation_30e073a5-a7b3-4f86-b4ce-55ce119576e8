replaces = { };

# Gloss – vivid, modern TUI theme (Catppuccin-like palette, Nerd icons)
# Uses 24-bit colours in Irssi (%Zrrggbb for fg).

abstracts = {
  line_start = "";
  timestamp = "%Z89b4fa[$*]%n";                     # blue timestamp

  hilight = "%Zcba6f7$*%n";                         # mauve
  error = "%Zf38ba8✖ %r$*%n";                       # red
  channel = "%Z89b4fa%_$*%_%n";                     # blue
  channelhilight = "%Zcba6f7$*%n";
  nick = "%n$*%n";
  nickhost = "$*";
  server = "%Z94e2d5$*%n";                           # teal
  comment = "%Z9399b2[$*]%n";                        # subtle grey
  reason = "%Z9399b2($* )%n";
  mode = "%Z94e2d5<$*>%n";

  # Events
  join_icon = "%Za6e3a1➜%n";        # green
  part_icon = "%Zfab387↩%n";        # peach
  quit_icon = "%Zf38ba8✖%n";        # red
  topic_icon = "%Zcba6f7✎%n";       # mauve

  # Message column: nick │ text (aligned by nm2)
  msgnick = "%N$0$1%n %Z45475a│%n %|";
  ownmsgnick = "%N$0$1%n %Z45475a│%n %|";
  ownnick = "%_$*%n";
  pubmsgnick = "{msgnick $0 $1-}";
  pubnick = "%n$*%n";
  pubmsgmenick = "{msgnick $0 $1-}";
  menick = "%Zf9e2af$*%n";          # yellow highlight
  pubmsghinick = "{msgnick $1 $0$2-}";
  msgchannel = "%Z89b4fa:$*%n";

  # Private
  privmsg = "%Z45475a[%n$0%Z45475a(%n{nickhost $1-}%Z45475a)]%n ";
  ownprivmsg = "%Z45475a[%nmsg%Z45475a(%n$1-%Z45475a)]%n ";
  ownprivmsgnick = "%N$*%n %Z45475a│%n ";
  ownprivnick = "%_$*%n";
  privmsgnick = "%N$*%n %Z45475a│%n ";

  # Actions / notices / ctcp
  action = "%Z94e2d5◆%n";
  ownaction = "{action } $0 $1-";
  pvtaction = "%Z45475a[%nquery%Z45475a]%n {action } -> $1-";
  pvtaction_query = "%Z94e2d5◆%n $* {action } ";
  pubaction = "{action } $*";

  ownnotice = "%Zcba6f7%nnotice%Zcba6f7%n ";
  notice = "%Z89b4fa%nnotice%Z89b4fa%n ";
  pvtnotice_host = "%Z9399b2(%n$*%Z9399b2)%n";
  servernotice = "{notice $*}";
  ownctcp = "%Z94e2d5[%nCTCP %c$0%Z94e2d5(%n$1%Z94e2d5)]%n ";
  ctcp = "%Z94e2d5$*%n";

  # Names / DCC
  names_nick = "%Zcdd6f4$0%w$[9]1-%n ";
  names_users = "(%Z94e2d5$0%w(%Z89b4fa$1%W))";
  names_channel = "%Z89b4fa$*%n";
  dcc = "%Z94e2d5$0%n $1 $3 $4 %Z94e2d5$5 $6 $7 $8-%n";
  dccfile = "%_$*%_";

  # Statusbar segments
  sb_background = "%0";
  sb = "%Z313244%Zcdd6f4$0%Z313244%n"; # dark segment with light text
  sbmode = "$0-";
  sbservertag = ":$0";
  sb_act_sep = "%Z45475a/";
  sb_act_text = "%Zcdd6f4$*%n";
  sb_act_msg = "%Za6e3a1$*%n";
  sb_act_hilight = "%Zcba6f7$*%n";
  sb_act_hilight_color = "$0$1-%n";

  sbstart = "";
  sbend = " ";
  topicsbstart = "{sbstart $*}";
  topicsbend = "{sbend $*}";
  prompt = "%Z313244%Zcdd6f4$*%Z313244%n ❯ ";

  # nm2/nickcolor integration
  nickalign = "";
  nickcolor = "%n";
  nicktrunc = "";
  cumode_space = " ";
};

formats = {
  "fe-common/core" = {
    join = "{join_icon} {channick_hilight $0} {chanhost_hilight $1}";
    part = "{part_icon} {channick_hilight2 $0} {chanhost_hilight $1} {reason $3}";
    kick = "%Zf38ba8✖%n {channick_hilight2 $0} by {nick $2} {reason $3}";
    quit = "{quit_icon} {channick_hilight2 $0} {chanhost_hilight $1} {reason $2}";
    servertag = "%Z313244%Zcdd6f4$0%Z313244%n ";
    invite = "%Za6e3a1✓%n {nick $0} → {channel $1}";
    new_topic = "{topic_icon} %YTopic%n by {channick $0} in {channel $1}: $2";
    topic_unset = "{topic_icon} %YTopic unset%n for {channel $1} by {channick $0}";

    pubmsg = "{pubmsgnick $nickalign$2 {pubnick $nickcolor$0$nicktrunc}}$1";
    own_msg = "{ownmsgnick $nickalign$2 {ownnick $nickcolor$0$nicktrunc}}$1";
    own_msg_channel = "{ownmsgnick $nickalign$3 {ownnick $nickcolor$0$nicktrunc}{msgchannel $1}}$2";
    pubmsg_me = "{pubmsgmenick $nickalign$2 {menick $0$nicktrunc}}$1";
    pubmsg_me_channel = "{pubmsgmenick $nickalign$3 {menick $0$nicktrunc}{msgchannel $1}}$2";
    pubmsg_hilight = "{pubmsghinick $0 $nickalign$3 $1$nicktrunc}$2";
    pubmsg_hilight_channel = "{pubmsghinick $0 $nickalign$4 $1$nicktrunc{msgchannel $2}}$3";
    pubmsg_channel = "{pubmsgnick $nickalign$3 {pubnick $nickcolor$0$nicktrunc}{msgchannel $1}}$2";
  };

  "fe-common/irc" = {
    inviting = "%Za6e3a1»%n Inviting {nick $0} to {channel $1}";
    topic_info = "%YTopic%n set by {channick $0} {comment $1}";
    server_chanmode_change = "%Z94e2d5ServerMode%n/{channelhilight $0} {mode $1} by {nick $2}";
    whois = "{nick $0} ({nickhost $1%R@%n$2}) {comment $4}%:{whois %_i%_rcname $3}";
    whois_channels = "{whois %_c%_hannels %|$1}";
    end_of_whois = "%Z313244%Zcdd6f4End of WHOIS%Z313244%n";
    who = "{channelhilight %C$[!10]0%n} %|{nick $[!9]1} %c$[!3]2%n $[!2]3 $4%R@%n$5 :: $6";
    ctcp_requested = "requested $2 from {nick $3} {ctcp {hilight $0} {comment $1}}";
  };

  nm2 = { neat_pad_char = " "; };
  "Irssi::Script::adv_windowlist" = {
    awl_display_key_active = "%0$N %Z45475a%n$H$C$S";
    awl_display_key = "$N %Z45475a%n$H$C$S";
    awl_viewer_item_bg = "%N";
    awl_display_header = "";
    awl_display_nokey = "$N %Z45475a%n$H$C$S";
    awl_display_nokey_visible = "$N %Z45475a%n$H$C$S";
    awl_display_key_visible = "$N %Z45475a%n$H$C$S";
    awl_display_nokey_active = "%0$N %Z45475a%n$H$C$S";
  };
  "Irssi::Script::nm2" = { neat_pad_char = " "; };
};


