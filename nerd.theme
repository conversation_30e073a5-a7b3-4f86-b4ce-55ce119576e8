replaces = { };

# Nerd/TUI inspired theme (powerline segments, subtle separators, Unicode icons)
abstracts = {
  line_start = "";
  timestamp = "%K%C$*%K%n"; # powerline-styled timestamp

  hilight = "%W$*%n";
  error = "%R✖ %r$*%n";
  channel = "%B%_$*%_";
  channelhilight = "%B$*%n";
  nick = "%n$*%n";
  nickhost = "$*";
  server = "%c$*%n";
  comment = "%K[$*]%n";
  reason = "%K($* )%n";
  mode = "%K<%C$*%K>%n";

  # joins/parts icons
  join_icon = "%G⬈%n";   # ascent arrow
  part_icon = "%Y⬊%n";   # descent arrow
  quit_icon = "%R✖%n";   # cross
  topic_icon = "%M✎%n";  # pencil

  # Message column: nick │ text
  msgnick = "%N$0$1%n %K│%n %|";   # $0 mode, $1 nick
  ownmsgnick = "%N$0$1%n %K│%n %|";
  ownnick = "%_$*%n";
  pubmsgnick = "{msgnick $0 $1-}";
  pubnick = "%N$*%n";
  pubmsgmenick = "{msgnick $0 $1-}";
  menick = "%Y$*%n";
  pubmsghinick = "{msgnick $1 $0$2-}";
  msgchannel = "%K:%B$*%n";

  # Private
  privmsg = "%K[%n$0%K(%n{nickhost $1-}%K)]%n ";
  ownprivmsg = "%K[%nmsg%K(%n$1-%K)]%n ";
  ownprivmsgnick = "%N$*%n %K│%n ";
  ownprivnick = "%_$*%n";
  privmsgnick = "%N$*%n %K│%n ";

  # Actions / notices / ctcp
  action = "%K*%n";
  ownaction = "{action } $0 $1-";
  pvtaction = "%K[%nquery%K]%n {action } -> $1-";
  pvtaction_query = "%K*%n $* {action } ";
  pubaction = "{action } $*";

  ownnotice = "%K%Mnotice%K%n ";
  notice = "%K%Cnotice%K%n ";
  pvtnotice_host = "%K(%n$*%K)%n";
  servernotice = "{notice $*}";
  ownctcp = "%K[%nCTCP %c$0%K(%n$1%K)]%n ";
  ctcp = "%c$*%n";

  # Names / DCC
  names_nick = "%C$0%w$[9]1-%n ";
  names_users = "(%c$0%w(%B$1%W))";
  names_channel = "%B$*%n";
  dcc = "%c$0%n $1 $3 $4 %c$5 $6 $7 $8-%n";
  dccfile = "%_$*%_";

  # Statusbar segments
  sb_background = "%0";
  sb = "%K%C$0-%K%n";
  sbmode = "$0-";
  sbservertag = ":$0";
  sb_act_sep = "%K/";
  sb_act_text = "%c$*";
  sb_act_msg = "%G$*";
  sb_act_hilight = "%M$*";
  sb_act_hilight_color = "$0$1-%n";

  sbstart = "";
  sbend = " ";
  topicsbstart = "{sbstart $*}";
  topicsbend = "{sbend $*}";
  prompt = "%K%C$*%K%n ❯ ";

  # nm2/nickcolor
  nickalign = "";
  nickcolor = "%n";
  nicktrunc = "";
  cumode_space = " ";
};

formats = {
  "fe-common/core" = {
    join = "{join_icon} {channick_hilight $0} {chanhost_hilight $1}";
    part = "{part_icon} {channick_hilight2 $0} {chanhost_hilight $1} {reason $3}";
    kick = "%R✖%n {channick_hilight2 $0} by {nick $2} {reason $3}";
    quit = "{quit_icon} {channick_hilight2 $0} {chanhost_hilight $1} {reason $2}";
    servertag = "%K%N$0%K%n ";
    invite = "%g%n {nick $0} → {channel $1}";
    new_topic = "{topic_icon} %YTopic%n by {channick $0} in {channel $1}: $2";
    topic_unset = "{topic_icon} %YTopic unset%n for {channel $1} by {channick $0}";

    pubmsg = "{pubmsgnick $nickalign$2 {pubnick $nickcolor$0$nicktrunc}}$1";
    own_msg = "{ownmsgnick $nickalign$2 {ownnick $nickcolor$0$nicktrunc}}$1";
    own_msg_channel = "{ownmsgnick $nickalign$3 {ownnick $nickcolor$0$nicktrunc}{msgchannel $1}}$2";
    pubmsg_me = "{pubmsgmenick $nickalign$2 {menick $0$nicktrunc}}$1";
    pubmsg_me_channel = "{pubmsgmenick $nickalign$3 {menick $0$nicktrunc}{msgchannel $1}}$2";
    pubmsg_hilight = "{pubmsghinick $0 $nickalign$3 $1$nicktrunc}$2";
    pubmsg_hilight_channel = "{pubmsghinick $0 $nickalign$4 $1$nicktrunc{msgchannel $2}}$3";
    pubmsg_channel = "{pubmsgnick $nickalign$3 {pubnick $nickcolor$0$nicktrunc}{msgchannel $1}}$2";
  };

  "fe-common/irc" = {
    inviting = "%g»%n Inviting {nick $0} to {channel $1}";
    topic_info = "%YTopic%n set by {channick $0} {comment $1}";
    server_chanmode_change = "%cServerMode%n/{channelhilight $0} {mode $1} by {nick $2}";
    whois = "{nick $0} ({nickhost $1%R@%n$2}) {comment $4}%:{whois %_i%_rcname $3}";
    whois_channels = "{whois %_c%_hannels %|$1}";
    end_of_whois = "%K%NEnd of WHOIS%K%n";
    who = "{channelhilight %C$[!10]0%n} %|{nick $[!9]1} %c$[!3]2%n $[!2]3 $4%R@%n$5 :: $6";
    ctcp_requested = "requested $2 from {nick $3} {ctcp {hilight $0} {comment $1}}";
  };

  nm2 = { neat_pad_char = " "; };
  "Irssi::Script::adv_windowlist" = {
    awl_display_key_active = "%0$N %K%n$H$C$S";
    awl_display_key = "$N %K%n$H$C$S";
    awl_viewer_item_bg = "%N";
    awl_display_header = "";
    awl_display_nokey = "$N %K%n$H$C$S";
    awl_display_nokey_visible = "$N %K%n$H$C$S";
    awl_display_key_visible = "$N %K%n$H$C$S";
    awl_display_nokey_active = "%0$N %K%n$H$C$S";
  };
  "Irssi::Script::nm2" = { neat_pad_char = " "; };
};


