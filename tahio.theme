replaces = { ":()" = "%K$*%n"; };
abstracts = {
  line_start = "%R➜ ";
  timestamp = "%G ➜ %C$*%K ⎢%n";
  # jasny zielony
  hilight = "%W$*";
  # biały
  error = "%r$*%n";
  # czerwony
  channel = "%B%_$*%_";
  # niebieski
  channel2 = "%W$C%n";
  # biały
  nick = "%n$*%n";
  # domyślny
  nickhost = "$*";
  server = "%g$*%N";
  # szary
  comment = "%W[%n$*%W]%N";
  # biały
  reason = "%W(%n$*%K)%N";
  # czarny
  mode = "%W<%B%_$*%W%_%W>%N";
  # niebieski
  channick_hilight = "%B$*%n";
  # niebieski
  channick_hilight2 = "%W$*%n";
  # biały
  chanhost_hilight = "%w<{nickhost $*}%w>";
  # jasny szary
  channick = "%g$*%n";
  # szary
  chanhost = "%W<%n%g{nickhost $*}%n%W>%n";
  # szary
  channelhilight = "%g$*%n";
  # szary
  ban = "$*";
  msgnick = "%n%K$0%W$1-%_%W:%n%|";
  # czarny
  ownmsgnick = "%n%K$0%W$1-%_%W:%n%|";
  # czarny
  ownnick = "%y%_$*%_";
  # żółty
  pubmsgnick = "{msgnick $0 $1-}";
  pubnick = "%_$*%_";
  pubmsgmenick = "{msgnick $0 %_$1-%_%n}";
  menick = "%R$*%n";
  # czerwony
  pubmsghinick = "{msgnick $1 $0$2-}";
  msgchannel = "%W:%n%_$*%_";
  # biały
  privmsg = "%K[%n%P$0%n%K(%n{nickhost $1-}%p%n%K)]%n ";
  ownprivmsg = "%K[%n%c%n%K(%C$1-%n%K)]%n ";
  ownprivmsgnick = "%W<%b$*%n%W>%N ";
  ownprivnick = "%_$*%_";
  privmsgnick = "%W<%_%C$*%n%W>%N ";
  action = "%K*%n";
  ownaction = "{action } %W$0 $1-";
  pvtaction = "%K[%cquery%n(%C$0%n)]%n {action } %w->%n $1-";
  pvtaction_query = "%W* $* {action } ";
  pubaction = "{action } %W$* } %g";
  whois = "$[12]0 : $1-";
  whois1 = "$[12]0 : $1-";
  whois2 = "$[12]0 : $1-";
  whois3 = "$[12]0 : $1-";
  ownnotice = "%K[%n%cnotice%n%K(%C$1-%n%K)]%n ";
  notice = "%R➜ %GNotice %K>%W>%B> %n{nick $0}: $1";
  pubnotice_channel = ":$*";
  pvtnotice_host = "%K(%n%c$*%n%K)%n";
  servernotice = "{notice $*}";
  ownctcp = "%K[%N%c$0%n%K(%C$1-%n%K)]%n ";
  ctcp = "%K>%n>%W>%n {nick $0} %g$1%n $2 %W$3%n $4 $5 %g$6%n";
  wallop = " $*: ";
  wallop_nick = " $*";
  wallop_action = " * $* ";
  netsplit = "%G$*";
  # jasny zielony
  netjoin = "%G$*";
  # jasny zielony
  names_nick = "%B$0%w$[9]1-%n ";
  # niebieski
  names_users = "(%c$0%w(%B$1%W))";
  # niebieski
  names_channel = "%B$*";
  # niebieski
  dcc = "%C$0%n $1 $3 $4 %c$5 $6 $7 $8-%n";
  dccfile = "%_$*%_";
  dccownmsg = "%K[%gdcc%n(%C$*%n)]%n ";
  dccownnick = "%c$*%n";
  dccownaction = "%K[%gdcc%g(%C$0%n)]%n {action } %w->%n $1-";
  dccmsg = "%K[%cdcc%n(%C$*%n)]%n ";
  dccquerynick = "%C$*%n";
  dccaction = "%K[%gdcc%n(%g$*)]%n {action }%n %|";
  sb_background = "%0";
  sb_topic_bg = "%G%W";
  # jasny zielony
  sb = "%W ➜ %n$0-%W ☣ %n";
  # biały
  prompt = "%K<%n%_$0%n%K>%n ";
  # czarny
  sbmode = "$0-";
  #sbaway = ":%K.%W: %WAway:%n %K'%c$A%K'%n";
  sbservertag = ":$0 %n(%cchange with ^X%n)";
  sbmore = "  %r<%R<%k< %nmore %k>%R>%r>  ";
  sblag = "{sb Lagging %r$0-%K seconds!}";
  sb_default_bg = "%4";
  sb_act_sep = "%K/";
  sb_act_text = "%R$*";
  # czerwony
  sb_act_msg = "%G$*";
  # biały
  sb_act_hilight = "%M$*";
  # magenta
  sb_act_hilight_color = "%B$0$1-%n";
  # niebieski
  sb_info1_bg = "%G";
  # jasny zielony
  sb_window_bg = "%B%W";
  # niebieski
  sb_window2_bg = "%B%W";
  # niebieski
  sb_info2_bg = "%G";
  # jasny zielony
  sb_usercount = "{sb %WN%wetwork:%n %K$tag }{sb %WU%wsers: %R$0 %K$1-";
  # biały i czerwony
  sb_uc_normal = "%WN%wormal %Y$*%R]";
  # biały i żółty
  sb_uc_ops = "%R[%WIRC%wOpers %_%y$mh_opercount  ➜  %WO%wpers %_%Y$*%c  ➜ ";
  # czerwony, biały, żółty
  sb_uc_voices = "%WV%woice %Y$*%c  ➜ ";
  # biały i żółty

  # Definicje dla skryptów nm2 i innych
  nickalign = "";
  nickcolor = "%n";
  nicktrunc = "";
  cumode_space = " ";
};

formats = {
  "fe-common/core" = {
    join = "%Kmode   %Wjoin  %K>%G>%W> %n{channick_hilight %W$0} {chanhost_hilight %w$1}";
    part = "%Kmode   %wpart   %W<%r<%R< %n{channick_hilight2 %w$0} {chanhost_hilight %K$1} %W{reason $3}";
    kick = "%Kmode   %RKICK   %R<%r<%K< %n{channick_hilight2 %W$0} by {nick $2} %W{reason $3}";
    quit = "%Kmode  %RKILLED %R<<< %n{channick_hilight2 %w$0}%n {chanhost_hilight %K$1}%n %W{reason $2}";
    notice = "%GNotice %K>%W>%B> %n{nick $0}: $1";
    quit_once = "{channel $3} {channick $0} {chanhost $1} {reason $2}";
    servertag = "%b[%W$0%b] ";
    invite = "%GInvites %K>%W>%B> %n{nick $0} to {channel $1}";
    new_topic = "%Kmode   %R#      %K>%W>%B> %nTopic was changed by %W{channick $0} in {channel $1} to: $2";
    topic_unset = "%Kmode   %R#   %R<%W<%B< %nTopic for %n{channel $1} %Wunset by %n{channick $0}";
    your_nick_changed = "You're Nickname %nis now %W{nick $1} ☣ ";
    nick_changed = "%Kmode   ~   {channick %W$0} %wis now %W{%Wchannick_hilight %W$1}";
    talking_in = "%G You are now talking in {%Rc%Whannel $0}";
    not_in_channels = "%R You are not on any %Rc%Whannels";
    names = "{names_users %W ➜ Users ☣ ) (channel {names_channel $0}} ➜ (%G$1) ☣ ";
    names_nick = "{names_nick $0 $1}";
    endofnames = "{channel %w$0}: %WTotal of {hilight %B((%G$1%R))%W} %wnicks%n {comment %WOps {hilight %W((%G$2%W))%W}, Voice %B(({hilight %G$4%R))%W}, Normal %B(({hilight %G$5%R))%W}";

    pubmsg = "{pubmsgnick $nickalign$2 {pubnick $nickcolor$0$nicktrunc}}$1";
    own_msg = "{ownmsgnick $nickalign$2 {ownnick $nickcolor$0$nicktrunc}}$1";
    own_msg_channel = "{ownmsgnick $nickalign$3 {ownnick $nickcolor$0$nicktrunc}{msgchannel $1}}$2";
    pubmsg_me = "{pubmsgmenick $nickalign$2 {menick $0$nicktrunc}}$1";
    pubmsg_me_channel = "{pubmsgmenick $nickalign$3 {menick $0$nicktrunc}{msgchannel $1}}$2";
    pubmsg_hilight = "{pubmsghinick $0 $nickalign$3 $1$nicktrunc}$2";
    pubmsg_hilight_channel = "{pubmsghinick $0 $nickalign$4 $1$nicktrunc{msgchannel $2}}$3";
    pubmsg_channel = "{pubmsgnick $nickalign$3 {pubnick $nickcolor$0$nicktrunc}{msgchannel $1}}$2";
    own_msg_private_query = "{ownprivmsgnick $nickalign{ownprivnick $nickcolor$2$nicktrunc}}$1";
    msg_private_query = "{privmsgnick $nickalign$nickcolor$0$nicktrunc}$2";
  };
  "fe-common/irc" = {
    inviting = "%K>%G>%W>%n Inviting {nick $0} to {channel $1}";
    # jasny zielony
    topic_info = "%Kmode   %nTopic set by %N{channick $0} {comment $1}";
    server_chanmode_change = "{netsplit %CServerMode%n}/{channelhilight $0} {mode $1} by {nick $2}";
    whois = "{nick $0} ({nickhost $1%R@%n$2}) {comment $4}%:{whois %_i%_rcname $3}";
    whowas = "           %WWHOWAS%K       %n%:%r*%n {nick $0} ({nickhost $1%R@%n$2})%:{whois %_i%_rcname $3}";
    whois_idle = "{whois3 %_i%_dle %|$1 days $2 hours $3 mins $4 secs}";
    whois_idle_signon = "{whois3 %_i%_dle %|$1 days $2 hours $3 mins $4 secs (signon: $5)}";
    whois_server = "{whois2 %_s%_erver %|$1 ($2)}";
    whois_oper = "{whois3 %_o%_perator {hilight $1}}";
    whois_registered = "{whois %_a%_uth has registered this nick}";
    whois_help = "{whois %_d%_uty is available for help}";
    whois_modes = " {whois %_m%_odes $1}";
    whois_realhost = "{whois %_h%_ostname $1-}";
    whois_usermode = "{whois %_u%_sermode $1}";
    whois_channels = "{whois %_c%_hannels %|$1}";
    whois_away = "{whois %_a%_way %W%|$1}";
    whois_special = "{whois  %|{hilight $1}}";
    end_of_whois = "%WEnd of WHOIS ☣ ";
    end_of_whowas = "%wEnd of WHOWAS ☣ ";
    whois_not_found = "%wThere is no such Nick:%R.%w {nick %W$0}";
    who = "{channelhilight %C$[!10]0%n} %|{nick $[!9]1} %c$[!3]2%n $[!2]3 $4%R@%n$5 :: $6";
    ctcp_requested = "requested $2 from {nick $3} {ctcp {hilight $0} {comment $1}}";
    own_action = "$nickalign{ownaction $0$nicktrunc}$1";
    action_private = "$nickalign{pvtaction $0$nicktrunc}$2";
    action_private_query = "$nickalign{pvtaction_query $0$nicktrunc}$2";
    action_public = "$nickalign{pubaction $0$nicktrunc}$1";
  };
  "fe-common/irc/dcc" = {
    own_dcc = "{dccownmsg {dccownnick $1}}$2";
    own_dcc_action = "{dccownaction_target $0 $1}$2";
    own_dcc_action_query = "{dccownaction $0}$2";
    own_dcc_ctcp = "{ownctcp ctcp $0}$1 $2";
    dcc_msg = "{dccmsg $0}$1";
    action_dcc = "{dccaction $0}$1";
    action_dcc_query = "{dccaction $0}$1";
    own_dcc_query = "{dccownmsg {dccownnick $0}}$2";
    dcc_msg_query = "{dccmsg $0}$1";
    dcc_ctcp = "{dcc %W>>> DCC CTCP {hilight $1} received from {hilight $0}: $2}";
    # biały dla wyróżnienia
  };
  nm2 = { neat_pad_char = " "; };
  "Irssi::Script::adv_windowlist" = {
    awl_display_key_active = "%0%B$N %B${cumode_space}%y$H$C$S";
    awl_display_key = "%B$N %y${cumode_space}%B$H$C$S";
    awl_viewer_item_bg = "%N";
    awl_display_header = "";
    awl_display_nokey = "%B$N %y${cumode_space}%B$H$C$S";
    awl_display_nokey_visible = "%B$N %M${cumode_space}%M$H%N$C$S";
    awl_display_key_visible = "%B$N %M${cumode_space}%M$H%N$C$S";
    awl_display_nokey_active = "%0%B$N %B${cumode_space}%y$H$C$S";
  };
  "Irssi::Script::nm2" = { neat_pad_char = " "; };
};
