servers = (
  {
    address = "sasl.irc.atw.hu";
    chatnet = "IRCnet";
    port = "6697";
    autoconnect = "no";
    use_ssl = "yes";
    use_tls = "yes";
    tls_verify = "yes";
    password = "";
    nick = "themeTEST";
    username = "themeTEST";
    realname = "https://www.ircnet.pl";
  },
  {
    address = "IRC.al";
    chatnet = "IRCnet2";
    port = "6697";
    autoconnect = "no";
    use_ssl = "yes";
    use_tls = "yes";
    tls_verify = "yes";
    password = "";
    nick = "themeTEST";
    username = "themeTEST";
    realname = "https://www.irc.al";
  },
  # Dodatkowe serwery IRCnet jako backup
  {
    address = "irc.ircnet.com";
    chatnet = "IRCnet";
    port = "6667";
    autoconnect = "no";
    use_ssl = "no";
    use_tls = "no";
    password = "";
    nick = "themeTEST";
    username = "themeTEST";
    realname = "https://www.ircnet.pl";
  },
  {
    address = "irc.ircnet.org";
    chatnet = "IRCnet";
    port = "6667";
    autoconnect = "no";
    use_ssl = "no";
    use_tls = "no";
    password = "";
    nick = "themeTEST";
    username = "themeTEST";
    realname = "https://www.ircnet.pl";
  }
);

aliases = {
  # --- Podstawowe aliasy ---
  J = "join";
  WJOIN = "join -window";
  WQUERY = "query -window";
  LEAVE = "part";
  BYE = "quit";
  EXIT = "quit";
  SIGNOFF = "quit";
  DESCRIBE = "action";
  DATE = "time";
  HOST = "userhost";
  LAST = "lastlog";
  SAY = "msg *";
  WI = "whois";
  WW = "whowas";
  W = "who";
  N = "names";
  M = "msg";
  T = "topic";
  C = "clear";
  CL = "clear";
  K = "kick";
  KB = "kickban";
  BANS = "ban";
  B = "ban";
  MUB = "unban *";
  UB = "unban";
  IG = "ignore";
  UNIG = "unignore";
  SB = "scrollback";
  WC = "window close";
  WN = "window new hide";
  SV = "say Irssi $J";
  GOTO = "sb goto";
  CHAT = "dcc chat";
  RUN = "script load";

  # --- Podstawowe narzędzia ---
  UPTIME = "exec - expr `date +%s` - \\$F | awk '{print \"Irssi uptime: \"int(\\\\\\$1/3600/24)\"d \"int(\\\\\\$1/3600%24)\"h \"int(\\\\\\$1/60%60)\"m \"int(\\\\\\$1%60)\"s\" }'";
  CALC = "exec - if which bc &>/dev/null\\; then echo '$*' | bc | awk '{print \"$*=\"$$1}'\\; else echo bc was not found\\; fi";
  SBAR = "statusbar";

  # --- Lag management ---
  lagsens = "set lag_check_time 1s; set lag_min_show 0.01s; echo Lag meter set Ultra Sensitive";
  lagnorm = "set lag_check_time 10s; set lag_min_show 1s; echo Lag meter set normal";
  laglow = "set lag_check_time 30s; set lag_min_show 5s; echo Lag meter set low";

  # --- Pomoc ---
  ahelp = "echo [\002irssi\002] Theme specific help; echo test2";

  # --- Channel modes ---
  li = "mode $C +I";
  le = "mode $C +e";
  lr = "mode $C +R";

  # --- Podstawowe komendy ---
  Q = "query";
  thx = "say thanks :>";
  figlet = "exec -o - figlet $*";
  bsay = "exec -o - figlet $*";
  nup = "nicklist scroll -25";
  nd = "nicklist scroll 25";
  s = "say =)";
  sec = "say wait a sec... =)";
  aw = "away zZzZ ($Z); say away $Z";
  timestamp = "%W⇉ $*%W ⇋ %n";
  silent = "hide target add $0";
  silence = "hide target add $0";
  unsilence = "hide target remove $0";
  output = "exec -o - $*; say output from: $*";
  quit = "echo \002Fail-Safe\002: If you really mean it use //quit";
  invited = "exec - mkdir -p ~/txt; echo $* >> ~/txt/invited.txt";
  disconnect = "echo \002Fail-Safe\002: If you really mean it use //disconnect";
  server = "server +$*; echo \002Multi-Server\002: Joining $*";
  type = "IRC";
  v = "voice";
  telnet = "window new hidden; window name telnet; exec -name telnet -nosh -interactive -window telnet $-";

  # --- Nowe aliasy testowe ---
  LASTWIN = "window last";
  RECON = "disconnect; reconnect";
  LIST = "quote list";
  AWAY = "away $*";
  BACK = "away";
  MYIP = "exec -o - curl ifconfig.me";
  VER = "echo Irssi version: $J";
  IGNORES = "ignore";
  BANSCHAN = "mode $C +b";
  OPS = "names $C | grep @";
  VOICES = "names $C | grep +";
  CLEARALL = "foreach window command clear";
  RELOADSCRIPTS = "script reload *";
  SCRIPTS = "script list";
  TESTJOIN = "msg * -- join testowy; join #test";
  TESTKICK = "msg * -- kick testowy; kick #test $N test";
  TESTPRIV = "msg $N testowa wiadomosc prywatna";

  # --- Multi-network management ---
  NET1 = "network IRCnet; set real_name https://www.ircnet.pl; echo Connected to IRCnet with realname: https://www.ircnet.pl";
  NET2 = "network IRCnet2; set real_name https://www.al; echo Connected to IRCnet2 with realname: https://www.al";
  NETSW = "network switch";
  NETLIST = "network list";
  NETSTATUS = "network status";

  # --- Quick server management ---
  CONN1 = "connect IRCnet; set real_name https://www.ircnet.pl";
  CONN2 = "connect IRCnet2; set real_name https://www.al";
  DISC1 = "disconnect IRCnet";
  DISC2 = "disconnect IRCnet2";

  # --- Enhanced window management ---
  WNET1 = "window new hidden; window name IRCnet; network IRCnet";
  WNET2 = "window new hidden; window name IRCnet2; network IRCnet2";
  WSPLIT = "window split; window name $0";

  # --- Window navigation ---
  WIN1 = "window 1";
  WIN2 = "window 2";
  WIN3 = "window 3";
  WIN4 = "window 4";
  WIN5 = "window 5";
  WIN6 = "window 6";
  WIN7 = "window 7";
  WIN8 = "window 8";
  WIN9 = "window 9";
  WIN10 = "window 10";

  # --- Network-specific joins ---
  J1 = "network IRCnet; join $*";
  J2 = "network IRCnet2; join $*";

  # --- Quick messaging across networks ---
  MSG1 = "network IRCnet; msg $*";
  MSG2 = "network IRCnet2; msg $*";

  # --- Network info ---
  NETINFO = "echo Current network: $tag; echo Server: $server_address; echo Port: $server_port";

  # --- IRCop help ---
  OPHELP = "echo === IRCop Commands ===; echo KLINE/UNKLINE - Global ban/unban; echo GLINE/UNGLINE - Global ban/unban; echo KILL - Kill user; echo RESTART/DIE - Server control; echo SQUIT - Disconnect server; echo CONNECT - Connect servers; echo LINKS/MAP - Network topology; echo STATS - Server statistics; echo ADMIN/INFO - Server info; echo OPERWALL/WALLOPS - Network announcements";

  # --- Script management ---
  RELOADALL = "script reload *";
  SCRIPTLIST = "script list";
  SCRIPTINFO = "script info $0";
  SCRIPTLOAD = "script load $0";
  SCRIPTUNLOAD = "script unload $0";

  # --- Translation shortcuts ---
  TREN = "tr en $*";
  TRPL = "tr pl $*";
  TRDE = "tr de $*";
  TRFR = "tr fr $*";
  TRES = "tr es $*";
  TRIT = "tr it $*";

  # --- Spellcheck shortcuts ---
  SPELL = "spellcheck $*";
  SPELLEN = "spellcheck en $*";
  SPELLPL = "spellcheck pl $*";

  # --- Keepnick management ---
  KEEPNICK = "keepnick $*";
  KEEPNICKLIST = "keepnick list";
  KEEPNICKADD = "keepnick add $*";
  KEEPNICKREMOVE = "keepnick remove $*";

  # --- Window management ---
  WINDOWLIST = "window list";
  WINDOWCLOSE = "window close";
  WINDOWNEW = "window new";
  WINDOWNAME = "window name $*";
  WINDOWMOVE = "window move $*";

  # --- Status bar management ---
  SBARLIST = "statusbar list";
  SBARADD = "statusbar add $*";
  SBARREMOVE = "statusbar remove $*";

  # --- Logging ---
  LOGON = "set autolog yes";
  LOGOFF = "set autolog no";
  LOGSTATUS = "set autolog";

  # --- Away management ---
  AWAYMSG = "away $*";
  AWAYBACK = "away";
  AWAYSTATUS = "echo Away status: $A";

  # --- Channel management ---
  CHANLIST = "list";
  CHANJOIN = "join $*";
  CHANPART = "part $*";
  CHANTOPIC = "topic $*";
  CHANMODES = "mode $C";

  # --- User management ---
  USERWHOIS = "whois $*";
  USERWHOWAS = "whowas $*";
  USERHOST = "userhost $*";
  USERTRACE = "trace $*";

  # --- Server management ---
  SERVERINFO = "quote info";
  SERVERVERSION = "quote version";
  SERVERMOTD = "quote motd";
  SERVERADMIN = "quote admin";
  SERVERLUSERS = "quote lusers";
  SERVERSTATS = "quote stats";

  # --- Quick responses ---
  SMILE = "say =)";
  WAIT = "say wait a sec... =)";
  OK = "say ok";
  YES = "say yes";
  NO = "say no";

  # --- System info ---
  SYSINFO = "exec -o - uname -a";
  UPTIME_SYS = "exec -o - uptime";
  MEMORY = "exec -o - free -h";
  DISK = "exec -o - df -h";
  PROCESSES = "exec -o - ps aux | head -10";

  # --- Network diagnostics ---
  PING = "exec -o - ping -c 3 $*";
  TRACEROUTE = "exec -o - traceroute $*";
  DNS = "exec -o - nslookup $*";
  WHOIS_SYS = "exec -o - whois $*";

  # --- File operations ---
  LS = "exec -o - ls -la $*";
  CAT = "exec -o - cat $*";
  HEAD = "exec -o - head -10 $*";
  TAIL = "exec -o - tail -10 $*";
  GREP = "exec -o - grep $*";

  # --- Weather and time ---
  WEATHER = "exec -o - curl wttr.in/$*";
  TIMEZONE = "exec -o - date";
  CALENDAR = "exec -o - cal";

  # --- Entertainment ---
  FORTUNE = "exec -o - fortune";
  COWSAY = "exec -o - cowsay $*";

  # --- Network tools ---
  IPINFO = "exec -o - curl ipinfo.io";
  SPEEDTEST = "exec -o - speedtest-cli --simple";
  PORTSCAN = "exec -o - nmap -p $*";

  # --- Quick help ---
  QHELP = "echo === Quick Help ===; echo NET1/NET2 - Switch networks; echo J1/J2 - Join channels on specific network; echo MSG1/MSG2 - Send messages on specific network; echo OPHELP - IRCop commands; echo TREN/TRPL - Quick translation; echo SPELL - Spellcheck; echo KEEPNICK - Nick management";

  # --- Theme management ---
  THEME = "set theme weechat.theme";
  THEMERESET = "set theme weechat.theme; echo Theme reset to weechat.theme";
  THEMELIST = "theme list";
  THEMEINFO = "theme info weechat.theme";

  # --- Dynamic realname management ---
  REALNAME1 = "set real_name https://www.ircnet.pl; echo Realname set for IRCnet: https://www.ircnet.pl";
  REALNAME2 = "set real_name https://www.al; echo Realname set for IRCnet2: https://www.al";
  REALNAME = "echo Current realname: $real_name";

  # --- Realname info ---
  REALNAMEINFO = "echo Current realname: $real_name; echo IRCnet realname: https://www.ircnet.pl; echo IRCnet2 realname: https://www.al";
  WII = "WHOIS $0 $0";
};
statusbar = {
  items = {
    barstart = "{sbstart}";
    barend = "{sbend}";
    time = "{sb %G$Z%n%W ⎢ %R➜%G  Connected: %G%W$tag%n %W>%G>%R> %WtahioN %R<%G<%W<%W}";
    user = "{sb %WN%wick ⇋ %R$cumode%w$N%W : ➜ %WS%wtatus {sbmode (%G+$usermode%n)}}";
    window = "%W ➜ %n{sbmode %WC%whanmodes (%G+$M%n%W) %G ➜ %WW%window: %R$winref }{sbaway $A}%W  %n";
    prompt = "%w<%W$itemname%w>: ";
    prompt_empty = "<%W$winname%n> ";
    topic = "%WTopic%G:%W $topic";
    lag = "{sb ⇉ %WL%wagging ⇋ %W: %G$0- }";
    act = "{sb %WActive Windows%G: $0-}";
    more = "%G-- %Wmore %G--";
  };
  default = {
    window = {
      type = "window";
      placement = "bottom";
      position = "0";
      visible = "active";
      items = {
        barstart = { priority = "100"; };
        time = { priority = "10"; };
        more = { priority = "-1"; alignment = "right"; };
        barend = { priority = "100"; alignment = "right"; };
      };
      disabled = "yes";
    };
    prompt = {
      type = "root";
      placement = "bottom";
      position = "100";
      visible = "active";
      items = {
        prompt = { priority = "-1"; };
        prompt_empty = { priority = "-1"; };
        input = { priority = "10"; };
        mh_sbuserinfo = { };
      };
    };
    topic = {
      type = "root";
      placement = "top";
      position = "1";
      visible = "always";
      items = { topic = { }; topic_empty = { }; };
    };
    info1 = {
      items = { user = { }; rotator = { }; window = { }; };
      position = "10";
      placement = "bottom";
    };
    info2 = {
      items = {
        time = { };
        more = { };
        upgradeinfo = { };
        act = { };
        lag = { };
      };
      position = "0";
    };
    add = { disabled = "yes"; };
    window1 = { disabled = "yes"; };
  };
};
settings = {
  core = {
    real_name = "https://www.al IRCnet2";
    # Domyślne dla IRCnet
    user_name = "testTheme";
    nick = "themeTEST";
    hostname = "";
    timestamp_format = "%H:%M:%S";
    override_coredump_limit = "no";
    resolve_prefer_ipv6 = "yes";
    server_reconnect_time = "300";
    recode_out_default_charset = "utf8";
    recode_autodetect_utf8 = "yes";
    recode_fallback = "ISO-8859-2";
    recode_transliterate = "yes";
    recode = "yes";
  };
  "irc/core" = {
    lag_check_time = "5s";
    # Sprawdzanie lagu co 5 sekund
    ctcp_version_reply = "";
    skip_motd = "no";
    lag_max_before_disconnect = "300";
    # Krótszy timeout
    cmds_max_at_once = "10";
    # Więcej komend na raz
  };
  "fe-common/core" = {
    theme = "weechat.theme";
    # Sztywno ustawiony motyw tahio
    activity_hide_level = "            KICK MODE  TOPIC NICK  QUIT CLIENTNOTICE CLIENTCRAP SNOTICE JOIN PART CRAP CLIENTCRAP";
    timestamps = "yes";
    timestamp_level = "ALL";
    hilight_color = "%M";
    autolog = "yes";
    autolog_path = "~/.irssi/logs/$tag/$0.log";
    term_charset = "UTF-8";
    use_status_window = "yes";
    hilight_nick_matches = "yes";
    window_auto_change = "yes";
    # Automatyczne przełączanie okien
  };
  "perl/core/scripts" = {
    nicklist_prefix_mode_op = "\\e[0m@\\e[0m\\e[39m";
    usercount_show_halfops = "no";
    rotator_chars = "%G%W-%k________%k %k_%k%W-%k_______%k %k__%k%W-%k______%k %k___%k%W-%k_____ %k____%k%W-%k____%k %k_____%G%W-%k___%k %k______%k%W-%k__%k %k_______%k%W-%k_%k %k________%k%W-%k";
    rotator_speed = "200";
    friends_autosave = "yes";
    loadavg_refresh = "30000";
    hitcount_refresh = "3600";
    nact_devices = "eth0";
    autoaway_timeout = "300";
    awl_shared_sbar = "OFF";
    neat_maxlength = "0";
    neat_dynamic = "yes";
    awl_viewer_launch_env = "NOTITLE=1";
    awl_viewer_tmux_position = "left";
    nick_char_sum_hash = "yes";
    nick_colors = "%r %R %g %G %y %b %B %c %C %X42 %X3A %X5E %X4N %X3H %X3C %X32";
    spellcheck_default_language = "pl_PL";
    spellcheck_print_suggestions = "no";
    spellcheck_word_color = "%R";
    spellcheck_word_input_color = "%U%R";
    masskick_default_reason = ".: tahioN :.";
    masskick_default_use_6method = "yes";
    chansort_autosort = "yes";
    hub_print_aw = "yes";
    autorejoin_delay = "10";
    translate_api_key = "AIzaSyDUfcmoiserVvtS_jlQ7Tj9s7xM-6Fyti0";
    translate_list_in = "{\"#ditalinux\":{\"target_lang\":\"pl\",\"source_lang\":\"it\"}}";
    translate_print_original = "yes";
    translate_list_out = "{\"gosub\":{\"target_lang\":\"en\",\"source_lang\":\"pl\"}}";
    hilightwin_show_network = "yes";
    fancy_abbrev = "no";
    awl_block = "18";
    chansort_configurable_autosort = "yes";
  };
  "fe-text" = {
    lag_min_show = "1s";
    scrollback_lines = "2000";
    paste_verify_line_count = "2";
    term_force_colors = "yes";
    colors = "yes";
    actlist_sort = "refnum";
  };
  type = "IRC";
  proxy = {
    irssiproxy_password = "tahioN1";
    irssiproxy_ports = "IRCnet=3992";
    irssiproxy_bind = "0.0.0.0";
    irssiproxy = "yes";
  };
};
logs = { type = "IRC"; };
chatnets = {
  IRCnet = {
    type = "IRC";
    #    sasl_mechanism = "PLAIN";
    #    sasl_username = "themeTEST";
    #    sasl_password = "mypass3000";
  };
  UnderNET = { type = "IRC"; };
  EvilNET = { type = "IRC"; };
  FreshChat = { type = "IRC"; };
  DALnet = { type = "IRC"; };
  PIRC = { type = "IRC"; };
  EFnet = { type = "IRC"; };
  QuakeNet = { type = "IRC"; };
  Freenode = { type = "IRC"; };
  IRCnet2 = {
    type = "IRC";
    #    sasl_mechanism = "PLAIN";
    #    sasl_username = "themeTEST";
    #    sasl_password = "mypass3000";
  };
};

keyboard = ( 
  { key = "meta-[M"; id = "command"; data = "mouse_xterm "; }
);
windows = {
  1 = {
    immortal = "yes";
    level = "ALL -PUBLIC -MSGS -ACTIONS -NOTICE -INVITES";
  };
};
ignores = ( { mask = "MrsEtCH"; level = "ALL"; } );
startup = {
  command = "/set theme weechat.theme; /set real_name https://www.ircnet.pl; /window new hidden; /window name Notices; /window level -ALL +NOTICES +INVITES; /window move 2; /window new hidden; /window name IRCnet; /network IRCnet; /window new hidden; /window name IRCnet2; /network IRCnet2; /set real_name https://www.al; /echo === Multi-Network IRC Setup Complete ===; /echo Theme: weechat.theme; /echo IRCnet realname: https://www.ircnet.pl; /echo IRCnet2 realname: https://www.al; /echo Use NET1/NET2 to switch networks; /echo Use OPHELP for IRCop commands; /echo Use HELP for quick help";
};

# Sekcja dla IRCop commands
# --- Custom sections (commented out as they're not standard Irssi config) ---
# ircop_commands = {
#   enabled = "yes";
#   network = "IRCnet2";
#   require_confirmation = "yes";
#   log_commands = "yes";
# };

# network_monitor = {
#   enabled = "yes";
#   check_interval = "60";
#   auto_reconnect = "yes";
#   max_reconnect_attempts = "5";
# };

# enhanced_logging = {
#   enabled = "yes";
#   log_ircop_actions = "yes";
#   log_network_switches = "yes";
#   log_server_connections = "yes";
#   separate_logs_per_network = "yes";
# };
