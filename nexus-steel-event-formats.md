# Nexus Steel Event Format Designs

*Comprehensive format string designs for all core IRC events with approved icons and colors*

---

## Core Event Formats

### Join/Part/Quit/Kick Events

#### Join Event
```irc
join = "%4▶%f %2CONNECTION%f {channick_hilight %c$0} {chanhost_hilight %8$1}";
```
- **Icon**: `▶` (U+25B6) - Start/Forward indicator
- **Colors**: `%4` electric-blue for icon, `%2` neon-green for "CONNECTION", `%c` cyan-steel for nick, `%8` shadow-gray for host
- **Length**: ~35-50 chars (within 80-column limit)

#### Part Event
```irc
part = "%3◀%f %6DISCONNECT%f {channick_hilight2 %c$0} {chanhost_hilight %8$1} %7{reason $3}";
```
- **Icon**: `◀` (U+25C0) - Stop/Backward indicator  
- **Colors**: `%3` amber-yellow for icon, `%6` cyan-steel for "DISCONNECT", `%c` for nick, `%8` for host, `%7` silver-gray for reason
- **Length**: ~40-55 chars

#### Quit Event
```irc
quit = "%1⏻%f %1TERMINATED%f {channick_hilight2 %c$0} {chanhost_hilight %8$1} %7{reason $2}";
```
- **Icon**: `⏻` (U+23FB) - Power/Quit symbol
- **Colors**: `%1` crimson-red for icon and label, `%c` for nick, `%8` for host, `%7` for reason
- **Length**: ~45-60 chars

#### Kick Event
```irc
kick = "%1⚠%f %1EXPELLED%f {channick_hilight2 %c$0} by {nick %d$2} %7{reason $3}";
```
- **Icon**: `⚠` (U+26A0) - Warning/Alert symbol
- **Colors**: `%1` crimson-red for icon and label, `%c` for victim, `%d` violet-purple for kicker, `%7` for reason
- **Length**: ~40-65 chars

#### Nick Change Event
```irc
nick_changed = "%5◆%f %5IDENTITY%f {channick %c$0} %7→%f {channick_hilight %a$1}";
```
- **Icon**: `◆` (U+25C6) - Component/Module symbol
- **Colors**: `%5` plasma-purple for icon and label, `%c` for old nick, `%7` arrow, `%a` lime-green for new nick
- **Length**: ~35-50 chars

### Topic Management

#### Topic Set
```irc
new_topic = "%4⚙%f %4TOPIC%f changed by {channick %c$0} in {channel $1}: %f$2";
```
- **Icon**: `⚙` (U+2699) - Configuration/Settings symbol
- **Colors**: `%4` electric-blue for icon and label, `%c` for nick, `%f` pure-white for topic text
- **Length**: ~45-70 chars

#### Topic Unset
```irc
topic_unset = "%8⚙%f %8TOPIC%f cleared by {channick %c$0} in {channel $1}";
```
- **Icon**: `⚙` (same as topic set for consistency)
- **Colors**: `%8` shadow-gray for icon and label (indicating removal), `%c` for nick
- **Length**: ~35-50 chars

### Away Status

#### Set Away
```irc
away = "%3◯%f %3AWAY%f %7Status set: %f$0";
```
- **Icon**: `◯` (U+25CB) - Pending/Away status
- **Colors**: `%3` amber-yellow for icon and label, `%7` silver-gray for "Status set", `%f` white for message
- **Length**: ~25-45 chars

#### Back from Away
```irc
unaway = "%2◎%f %2ACTIVE%f %7Status cleared";
```
- **Icon**: `◎` (U+25CE) - Complete/Active status
- **Colors**: `%2` neon-green for icon and label, `%7` silver-gray for status text
- **Length**: ~20-35 chars

### Server Connection

#### Server Connect
```irc
server_connect = "%2⚡%f %2SYSTEM%f ⟪ Connected to %4$0%f";
```
- **Icon**: `⚡` (U+26A1) - Power/Energy symbol, `⟪` (U+27EA) - Input/Beginning
- **Colors**: `%2` neon-green for icon and label, `%4` electric-blue for server name, `%f` white for text
- **Length**: ~30-50 chars

#### Server Disconnect
```irc
server_disconnect = "%1⚡%f %1SYSTEM%f ⟫ Disconnected from %8$0%f";
```
- **Icon**: `⚡` (same as connect), `⟫` (U+27EB) - Output/End
- **Colors**: `%1` crimson-red for icon and label, `%8` shadow-gray for server name
- **Length**: ~35-55 chars

---

## WHOIS Reply Formats

### Main WHOIS Entry
```irc
whois = "%6∞%f %6INFO%f {nick %c$0} %8({nickhost %c$1%8@%c$2}) %7$4 %8│%7 {whois ircname %f$3}";
```
- **Icon**: `∞` (U+221E) - Infinite/Continuous symbol
- **Colors**: `%6` cyan-steel for icon/label, `%c` for nick/host, `%8` for separators, `%7` for metadata, `%f` for ircname
- **Length**: ~60-75 chars

### WHOIS Server
```irc
whois_server = "%6∞%f %6SERVER%f %8│%f %c$1 %8(%7$2%8)";
```
- **Length**: ~30-50 chars

### WHOIS Idle
```irc
whois_idle = "%6∞%f %6IDLE%f %8│%f %7$1d %7$2h %7$3m %7$4s";
```
- **Length**: ~25-40 chars

### WHOIS Channels
```irc
whois_channels = "%6∞%f %6CHANNELS%f %8│%f %c$1";
```
- **Length**: Variable based on channel list

---

## Notice Formats

### Public Notice
```irc
notice = "%d◆%f %dNOTICE%f %4▶%c▶%a▶%f {nick $0}: $1";
```
- **Icon**: `◆` (U+25C6) - Component symbol, `▶` progression arrows
- **Colors**: `%d` violet-purple for icon/label, progression from `%4` to `%c` to `%a`
- **Length**: ~35-70 chars

### Private Notice
```irc
ownnotice = "%d⟨%fnotice%d⟩%6⟨%f$1-%6⟩%f ";
```
- **Icons**: `⟨⟩` (U+27E8/U+27E9) - Parameter brackets
- **Colors**: `%d` violet-purple for brackets, `%6` cyan-steel for target brackets
- **Length**: ~25-50 chars

### Server Notice
```irc
servernotice = "%2⬢%f %2SERVER%f %4▶%f $0";
```
- **Icon**: `⬢` (U+2B22) - Secure/Protected symbol
- **Colors**: `%2` neon-green for icon/label, `%4` electric-blue arrow
- **Length**: ~20-45 chars

---

## CTCP Formats

### CTCP Request
```irc
ctcp = "%5⧐%f %5CTCP%f %4▶%c▶%a▶%f {nick $0} %a$1%f $2 %c$3%f $4 $5 %a$6%f";
```
- **Icon**: `⧐` (U+29D0) - Transfer/Flow symbol
- **Colors**: `%5` plasma-purple for icon/label, progression arrows, alternating colors for parameters
- **Length**: ~40-80 chars

### Own CTCP
```irc
ownctcp = "%5⟨%f$0%5⟩%6⟨%f$1-%6⟩%f ";
```
- **Icons**: `⟨⟩` parameter brackets
- **Colors**: `%5` plasma-purple, `%6` cyan-steel for target
- **Length**: ~20-40 chars

---

## Statusbar Formats

### Main Statusbar
```irc
sb = "%4▸%f $0-%4 ◦%f";
```
- **Icons**: `▸` (U+25B8) - Small arrow, `◦` (bullet)
- **Colors**: `%4` electric-blue for decorative elements
- **Length**: Variable based on content

### Activity Separator
```irc
sb_act_sep = "%8/%f";
```
- **Colors**: `%8` shadow-gray for separator
- **Length**: 1 char

### Activity Indicators
```irc
sb_act_text = "%7$*";      # Normal activity - silver-gray
sb_act_msg = "%c$*";       # Messages - cyan-steel  
sb_act_hilight = "%a$*";   # Highlights - lime-green
```

### Lag Indicator
```irc
sblag = "%1⚠%f %1LAG%f %7$0-%f seconds";
```
- **Icon**: `⚠` warning symbol
- **Colors**: `%1` crimson-red for critical lag warning
- **Length**: ~15-30 chars

### More Indicator  
```irc
sbmore = " %1◀%9◀%8◀ %fmore %8▶%9▶%1▶ ";
```
- **Icons**: `◀▶` directional arrows
- **Colors**: Gradient from `%1` to `%8` showing more content available
- **Length**: ~15 chars

---

## Implementation Guidelines

### Color Usage Rules
1. **System Events**: Primary colors (`%2` green for success, `%1` red for errors, `%4` blue for info)
2. **User Events**: Secondary colors (`%c` cyan-steel, `%7` silver-gray, `%8` shadow-gray)
3. **Special Actions**: Accent colors (`%5` plasma-purple, `%d` violet-purple, `%a` lime-green)

### Icon Consistency
- **Connection Events**: `▶◀` directional arrows
- **System Events**: `⚡` power symbol  
- **Info/Status**: `∞◆` infinite/component symbols
- **Warnings**: `⚠` warning triangle
- **Security/Auth**: `⬢` hexagon symbol
- **Data Flow**: `⧐` transfer symbol
- **Configuration**: `⚙` gear symbol

### Width Optimization
- **Target Width**: 60-75 characters for most events
- **Maximum Width**: 80 characters
- **Variable Content**: Use dynamic width for user-generated content (topics, quit messages)
- **Fixed Elements**: Keep labels and icons consistent width

### UTF-8 Fallbacks
For terminals without UTF-8 support:
- `▶` → `>`
- `◀` → `<`  
- `⚡` → `*`
- `◆` → `+`
- `⚠` → `!`
- `⬢` → `#`
- `∞` → `~`
- `⧐` → `=`
- `⚙` → `@`

---

## Testing Checklist

### Visual Verification
- [ ] All events display properly in 80-column terminal
- [ ] Colors appear correctly with theme
- [ ] Icons render properly with UTF-8 support
- [ ] Fallback characters work in ASCII mode

### Functional Testing  
- [ ] All placeholder variables populate correctly
- [ ] Dynamic content (nicks, channels, messages) displays properly
- [ ] Long content wraps appropriately
- [ ] Color codes don't interfere with text selection

### Compatibility Testing
- [ ] Works with nm2 script alignment
- [ ] Compatible with adv_windowlist formatting
- [ ] Proper integration with existing abstracts
- [ ] No conflicts with existing replaces/formats

---

*This document provides the complete format string designs for implementing the Nexus Steel visual style across all IRC events and status indicators.*
