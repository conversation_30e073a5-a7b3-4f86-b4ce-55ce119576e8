# Dokumentacja Motywu Nexus Steel

*Szczegółowa analiza motywu nexus_steel.theme - cyberpunk IRC theme*

---

## Przegląd Motywu

**Nexus Steel v1.0** to nowoczesny motyw cyberpunk dla irssi, zaprojektowany dla terminali 21. wieku. Łączy elektryczne błękity, neonowe zielenie i przemysłowe szare odcienie, tworząc futurystyczny interfejs o wysokim kontraście.

### Kluczowe Cechy
- **Paleta Cyberpunk**: 16-kolorowa paleta z extended colors
- **UTF-8 Icons**: Nowoczesne symbole Unicode
- **24-bit Color Support**: Zaawansowane kolorowanie HTML hex
- **AWL/nm2 Integration**: Pełna integracja z skryptami UI
- **High Contrast**: Optymalizacja dla czytelności

---

## Paleta Kolorów

### Podstawowe Kolory Irssi
```
%K/%k - void-black/shadow-gray     %R/%r - crimson-red/hot-red
%G/%g - neon-green/lime-green      %Y/%y - amber-yellow/gold-yellow  
%B/%b - electric-blue/sky-blue     %M/%m - plasma-purple/violet-purple
%C/%c - cyan-steel/aqua-cyan       %W/%w - silver-white/light-gray
%N    - reset to default           %_    - underline
```

### Mapowanie Semantyczne
| Kod | Nazwa Koloru | Hex Value | Kontekst Użycia |
|-----|--------------|-----------|-----------------|
| `%K` | void-black | `#000000` | Tło, cienie |
| `%R` | crimson-red | `#ff3366` | Błędy, alerty krytyczne |
| `%G` | neon-green | `#33ff66` | Sukces, aktywne procesy |
| `%Y` | amber-yellow | `#ffcc00` | Ostrzeżenia, stany oczekujące |
| `%B` | electric-blue | `#00aaff` | Główny akcent, linki |
| `%M` | plasma-purple | `#cc66ff` | Drugorzędny akcent, specjalne |
| `%C` | cyan-steel | `#00ffcc` | Info, metadane |
| `%W` | silver-white | `#cccccc` | Standardowy tekst |

### Extended Colors (24-bit)
```
%Z4C9AFF - Electric Blue Gradient
%ZFF6B6B - Soft Red
%Z6BFF6B - Neon Green  
%ZFFFF6B - Bright Yellow
%ZFF6BFF - Magenta Pink
%Z6BFFFF - Cyan Blue
```

---

## Sekcja Abstracts

### Core Visual Elements (34-46)
```irssi
line_start = "%N";
timestamp = "%b$*%C❱%N";                    # Sky-blue time + cyan arrow
hilight = "%Y%_$*%_%N";                     # Amber underlined highlights
error = "%R☠ $*%N";                         # Crimson skull for errors
channel = "%b%_$*%_%N";                     # Sky-blue underlined channels
server = "%G$*%N";                          # Neon-green servers
comment = "%k❮%N$*%k❯%N";                   # Shadow-gray angle brackets
mode = "%C❮%B%_$*%N%_%C❯%N";                # Cyan-blue mode brackets
```

### Nick Highlighting (48-55)
```irssi
channick_hilight = "%Y%_$*%_%N";            # Amber underlined for mentions
channick_hilight2 = "%y$*%N";               # Gold-yellow secondary
chanhost_hilight = "%k❮{nickhost %y$*}❯%k"; # Shadow brackets with gold
channick = "%w$*%N";                        # Silver-gray normal nicks
channelhilight = "%B$*%N";                  # Electric-blue channel highlights
```

### Message Styling (57-66)
```irssi
msgnick = "%w$0%k$1-%B⧽%N%| ";              # White nick + blue separator
ownmsgnick = "%g$0%k$1-%B⧽%N%| ";           # Green own nick + blue separator
ownnick = "%g%_$*%_%N";                     # Green underlined own nick
pubmsghinick = "{msgnick %Y$1 %y$0%Y$2-}";  # Amber highlight nick format
msgchannel = "%B⧽%N%_$*%_";                 # Blue separator + underlined channel
```

### Private Messages (68-73)
```irssi
privmsg = "%C❮%b$0%C❯%k❮{nickhost $1-}❯%N ";      # Cyan-blue private format
ownprivmsg = "%C❮%g$0%C❯%k❮$1-❯%N ";              # Cyan-green own private
ownprivmsgnick = "%C❮%g$*%C❯%N ";                  # Cyan brackets green nick
privmsgnick = "%C❮%b$*%C❯%N ";                     # Cyan brackets blue nick
```

### Actions (75-80)
```irssi
action = "%k⚬%N";                           # Shadow-gray bullet
ownaction = "{action} %g$0 $1-";            # Green own actions
pvtaction = "%C❮%Bquery%C❯%k❮$0❯%N {action} %B➢%N $1-"; # Cyan query format
pubaction = "{action} %b$* %k⚬%N";          # Sky-blue public actions
```

### Notices (88-93)
```irssi
ownnotice = "%C❮%Btransmission%C❯%k❮$1-❯%N ";      # Cyberpunk transmission
notice = "%m⬢%N %mSIGNAL%N %B⟩%b⟩%g⟩%N {nick $0}: $1"; # Multi-color signal
servernotice = "%G☠%N %GSYSTEM%N %B⟩%N $0";        # Green skull system notice
```

### CTCP (95-97)
```irssi
ownctcp = "%M❮%N$0%M❯%C❮%N$1-%C❯%N ";              # Magenta-cyan CTCP
ctcp = "%M⚙%N %MPROBE%N %B⟩%b⟩%g⟩%N {nick $0} %g$1%N $2 %b$3%N $4 $5 %g$6%N";
```

---

## UTF-8 Icons Palette

### Core System Icons
| Icon | Unicode | Hex Code | Znaczenie | Kontekst |
|------|---------|----------|-----------|----------|
| ⚡ | U+26A1 | `\u26A1` | Power, Energy | Aktywne procesy, statusbar |
| ❮❯ | U+276E/F | `\u276E/F` | Angle brackets | Komentarze, hosty |
| ⧽ | U+29FD | `\u29FD` | Right angle | Separatory wiadomości |
| ⬢ | U+2B22 | `\u2B22` | Hexagon | Bezpieczeństwo, notices |
| ⚙ | U+2699 | `\u2699` | Gear | Konfiguracja, CTCP |
| ☠ | U+2620 | `\u2620` | Skull | Błędy, system notices |
| ❱ | U+2771 | `\u2771` | Right angle | Timestamp separator |
| ⚗ | U+2697 | `\u2697` | Alembic | Invites, chemical process |

### Status Indicators
| Icon | Unicode | Znaczenie | Kolor |
|------|---------|-----------|-------|
| ⥢ | U+2962 | Join (breach) | %G (neon-green) |
| ⥤ | U+2964 | Part (withdraw) | %Y (amber-yellow) |
| ☠ | U+2620 | Kick (dropped) | %R (crimson-red) |
| ⏻ | U+23FB | Quit (deprecated) | %R (crimson-red) |
| ⇶ | U+21F6 | Nick change (evolved) | %M (plasma-purple) |
| ⏸ | U+23F8 | Away (idle) | %Y (amber-yellow) |
| ⏵ | U+23F5 | Unaway (active) | %G (neon-green) |

### Directional Indicators
| Icon | Unicode | Znaczenie | Użycie |
|------|---------|-----------|--------|
| ⟩ | U+27E9 | Right angle | Multi-level separators |
| ➢ | U+27A2 | Arrow right | Actions, flow |
| ◎ | U+25CE | Target | Entities, totals |
| ◆ | U+25C6 | Diamond | Components, visible windows |

---

## Sekcja Formats

### Core Events (161-200)

#### Join/Part/Kick/Quit (163-166)
```irssi
join = "%G⥢%N {channick_hilight %Y$0} {chanhost_hilight %k$1} %Ghas breached%N {channel %M$2}";
part = "%Y⥤%N {channick_hilight2 %Y$0} {chanhost_hilight %k$1} %Yhas withdrawn from%N {channel %M$2} %w{reason $3}";
kick = "%R☠%N {channick_hilight2 %b$0} %Rdropped%N %wby {nick %m$2} {channel $1} %w{reason $3}";
quit = "%R⏻%N {channick_hilight2 %Y$0} {chanhost_hilight %k$1} %Rhas been deprecated%N %w{reason $2}";
```

**Cyberpunk Terminology**:
- Join → "has breached" (włamanie)
- Part → "has withdrawn from" (wycofanie)
- Kick → "dropped" (porzucenie)
- Quit → "has been deprecated" (zdeprecjonowanie)

#### Server Events (185-187)
```irssi
server_connect = "%GSYSTEM%N %G⚡%N Interface %BONLINE%N to %B$0%N";
server_disconnect = "%RSYSTEM%N %R⚡%N Interface %ROFFLINE%N from %k$0%N";
```

#### Nick Changes (173-174)
```irssi
your_nick_changed = "%MEVOLVED%N %M⇶%N %winto {nick %g$1} %Csynchronized%N";
nick_changed = "%MEVOLUTION%N %M⬢%N {channick %b$0} %whas evolved into {channick_hilight %g$1}";
```

### Enhanced WHOIS System (207-224)

#### 24-bit Color WHOIS
Motyw używa zaawansowanego systemu kolorowania WHOIS z HTML hex colors:

```irssi
whois = "%N%8%Z4C9AFF%Z4C9AFF SCAN %N%Z4C9AFF%z708090%z708090 %z708090%k%W$0%z708090 %W(%z708090%Y$1%W@%Y$2%W)%z708090 identity:%z708090 %C$3 %z708090%c%N%Z708090";
```

**Struktura WHOIS**:
- `%Z4C9AFF` - Electric blue background
- `%z708090` - Slate gray details
- Cyberpunk labels: SCAN, ARCHIVE, AFFK, NODE, ADMIN, VERIFIED

#### WHOIS Categories
| Format | Label | Color | Znaczenie |
|--------|-------|-------|-----------|
| whois | SCAN | `%Z4C9AFF` | Podstawowe info |
| whowas | ARCHIVE | `%ZFF6B6B` | Archiwalne dane |
| whois_idle | AFFK | `%Z6BFF6B` | Away from keyboard |
| whois_server | NODE | `%ZFF6BFF` | Server info |
| whois_oper | ADMIN | `%Z6BFFFF` | Operator status |
| whois_registered | VERIFIED | `%ZFF6BD7` | Registered nick |
| whois_channels | HIDEOUT | `%Z6BFFD7` | Channel list |
| whois_not_found | Entity not found | `%ZFF6B9E` | Nick not found |

### Message Formats with nm2 Support (189-199)

#### Nick Alignment Integration
```irssi
pubmsg = "{pubmsgnick $nickalign$2 {pubnick $nickcolor$[.15]0$nicktrunc}}$1";
own_msg = "{ownmsgnick $nickalign$2 {ownnick $nickcolor$[.15]0$nicktrunc}}$1";
pubmsg_hilight = "{pubmsghinick $0 $nickalign$3 $[.15]1$nicktrunc}$2";
```

**nm2 Variables**:
- `$nickalign` - Nick alignment padding
- `$nickcolor` - Dynamic nick coloring
- `$nicktrunc` - Nick truncation
- `$[.15]` - Max 15 characters width

---

## AWL Integration (253-292)

### Advanced Window List Styling
```irssi
"Irssi::Script::adv_windowlist" = {
  awl_display_key_active = "%B⚡%N$N%b${cumode_space}%G$H$C$S";
  awl_display_key = "  %w$N%k${cumode_space}%w$H$C$S";
  awl_display_nokey_active = "%B⚡%N$N%b${cumode_space}%G$H$C$S";
  awl_display_nokey_visible = "%C◆%N %w$N %b${cumode_space}%C$H%N$C$S";
};
```

**AWL Elements**:
- `⚡` - Power symbol dla aktywnych okien
- `◆` - Diamond symbol dla widocznych okien
- Activity levels: none(%w) → low(%b) → medium(%Y) → high(%R)

### Activity Level Colors
```irssi
awl_data_level_none = "%w";     # Silver-gray (brak aktywności)
awl_data_level_low = "%b";      # Sky-blue (niska aktywność)
awl_data_level_medium = "%Y";   # Amber-yellow (średnia aktywność)
awl_data_level_high = "%R";     # Crimson-red (wysoka aktywność)
```

---

## nm2 Integration (298-310)

### Nick Alignment Configuration
```irssi
"Irssi::Script::nm2" = {
  neat_pad_char = " ";
  neat_left_actions = "0";
  neat_right_actions = "1";
  neat_left_messages = "0";
  neat_right_messages = "1";
  neat_maxlength = "15";
  neat_colors = "1";
  neat_shrink_uniq = "1";
  neat_colorize = "1";
  neat_allow_shrinking = "1";
  neat_melength = "15";
};
```

**Konfiguracja**:
- Right-aligned nicks dla wiadomości i akcji
- Max 15 znaków długości
- Intelligent shrinking
- Full colorization support
- Dynamic width adjustment

---

## Kluczowe Funkcjonalności

### 1. Cyberpunk Aesthetics
- **Futuristic terminology**: "breached", "deprecated", "evolved"
- **Tech labels**: SCAN, NODE, ADMIN, VERIFIED, ENTITY
- **Industrial colors**: Steel grays, electric blues, neon greens

### 2. High Contrast Design
- **Background**: Void black (%K)
- **Primary text**: Silver white (%W)
- **Accents**: Electric blue (%B), neon green (%G)
- **Alerts**: Crimson red (%R), amber yellow (%Y)

### 3. UTF-8 Icon System
- **Consistent iconography**: ⚡⬢⚙☠❮❯⧽
- **Semantic meaning**: Each icon has specific purpose
- **Fallback support**: ASCII alternatives available

### 4. 24-bit Color Support
- **HTML hex colors**: %Z4C9AFF, %ZFF6B6B, etc.
- **Gradient effects**: Smooth color transitions
- **Enhanced WHOIS**: Rich color coding

### 5. Script Integration
- **AWL compatibility**: Full window list styling
- **nm2 support**: Perfect nick alignment
- **nickcolor integration**: Dynamic nick coloring
- **Statusbar harmony**: Consistent visual language

---

## Accessibility Features

### High Contrast Mode
- Always void-black background (%K)
- Pure-white primary text (%W)
- Limited accent colors for maximum contrast

### Color Blind Support
- Never rely on color alone for critical information
- Always pair colors with icons for semantic meaning
- Distinct brightness levels for different states

### Terminal Compatibility
- 8-color fallbacks for older terminals
- ASCII alternatives for non-UTF-8 terminals
- Tested in major terminal emulators

---

*Motyw Nexus Steel v1.0 - Modern cyberpunk IRC theme for the 21st century terminal experience*
