    whois = "%N%8%Z4C9AFF%Z4C9AFF SCAN %N%Z4C9AFF%z708090%z708090 %z708090%k%W$0%z708090 %W(%z708090%Y$1%W@%Y$2%W)%z708090 identity:%z708090 %C$3 %z708090%c%N%Z708090";
    whowas = "%N%8%ZFF6B6B%ZFF6B6B ARCHIVE %N%ZFF6B6B%z708090%z708090 %z708090%W*%z708090 %W$0%z708090 %W(%z708090%Y$1%W@%Y$2%W)%z708090 identity:%z708090 %C$3 %z708090%c%N%Z708090";
    whois_idle = "%N%8%Z6BFF6B%Z6BFF6B AFFK %N%Z6BFF6B%z708090%z708090 %z708090%C$1d%z708090 %C$2h%z708090 %C$3m%z708090 %C$4s %z708090%c%N%Z708090";
    whois_idle_signon = "%N%8%ZFFFF6B%ZFFFF6B AFFK %N%ZFFFF6B%z708090%z708090 %z708090%C$1d%z708090 %C$2h%z708090 %C$3m%z708090 %C$4s%z708090 %W(login:%z708090 %Y$5%W)%z708090 %c%N%Z708090";
    whois_server = "%N%8%ZFF6BFF%ZFF6BFF NODE %N%ZFF6BFF%z708090%z708090 %z708090%C$1%z708090 %W(%z708090%B$2%W)%z708090 %c%N%Z708090";
    whois_oper = "%N%8%Z6BFFFF%Z6BFFFF ADMIN %N%Z6BFFFF%z708090%z708090 %z708090%Y$1%z708090 %c%N%Z708090";
    whois_registered = "%N%8%ZFF6BD7%ZFF6BD7 VERIFIED %N%ZFF6BD7%z708090%z708090 %z708090%Widentity%z708090 %Wauthenticated%z708090 %c%N%Z708090";
    whois_help = "%N%8%Z6BBFFF%Z6BBFFF SUPPORT %N%Z6BBFFF%z708090%z708090 %z708090%Wavailable%z708090 %Wfor%z708090 %Wassistance%z708090 %c%N%Z708090";
    whois_modes = "%N%8%ZB6FF6B%ZB6FF6B PERMS %N%ZB6FF6B%z708090%z708090 %z708090%C$1%z708090 %c%N%Z708090";
    whois_realhost = "%N%8%ZFFB66B%ZFFB66B TRACE %N%ZFFB66B%z708090%z708090 %z708090%C$1-%z708090 %c%N%Z708090";
    whois_usermode = "%N%8%ZD76BFF%ZD76BFF STATUS %N%ZD76BFF%z708090%z708090 %z708090%C$1%z708090 %c%N%Z708090";
    whois_channels = "%N%8%Z6BFFD7%Z6BFFD7 HIDEOUT %N%Z6BFFD7%z708090%z708090 %z708090%B$1%z708090 %c%N%Z708090";
    whois_away = "%N%8%ZFF9E6B%ZFF9E6B AWAY %N%ZFF9E6B%z708090%z708090 %z708090%Y$1%z708090 %c%N%Z708090";
    whois_special = "%N%8%Z9E6BFF%Z9E6BFF EXTRAS %N%Z9E6BFF%z708090%z708090 %z708090%Y$1%z708090 %c%N%Z708090";
    end_of_whois = "%N%8%Z6BFF9E%Z6BFF9E SYNCED %N%Z6BFF9E%z708090%z708090 %z708090%Wend%z708090 %Wof%z708090 %Wwhois%z708090 %c%N%Z708090";
    end_of_whowas = "%N%8%ZBFFF6B%ZBFFF6B SYNCED %N%ZBFFF6B%z708090%z708090 %z708090%Wend%z708090 %Wof%z708090 %Wwhowas%z708090 %c%N%Z708090";
    whois_not_found = "%N%8%ZFF6B9E%ZFF6B9E Entity not found %N%ZFF6B9E%z708090%z708090 %z708090%R$0%z708090 %c%N%Z708090";
