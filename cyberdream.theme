
# cyberdream.theme
# A modern, elegant, and fresh theme inspired by cyberpunk and synthwave aesthetics.

replaces = { ":()" = "%K$*%n"; };

abstracts = {
  # General
  line_start = "%m·%n ";
  timestamp = "%M❯%n %c$*%n";
  error = "%RError: $*$*%n";
  hilight = "%M$*%n";
  comment = "%c(%n$*%c)%N";
  reason = "%c(%n$*%K)%N";

  # Channels and Nicks
  channel = "%c%_$*%_";
  nick = "%m$*%n";
  ownnick = "%Y%_$*%_";
  nickhost = "$*";
  channick = "%m$*%n";
  chanhost = " %c[%m{nickhost $*}%c]%n";
  channick_hilight = "%M$*%n";
  chanhost_hilight = " %c[{nickhost $*}]%c";
  channelhilight = "%c$*%n";

  # Messages
  msgnick = "%m$0%M$1-%n%c:%n ";
  ownmsgnick = "%y$0%Y$1-%n%c:%n ";
  pubmsgnick = "{msgnick $0 $1-}";
  pubnick = "%_$*%_";
  pubmsgmenick = "{msgnick $0 %_$1-%_%n}";
  menick = "%M$*%n";
  pubmsghinick = "{msgnick $1 $0$2-}";
  msgchannel = ":%c%_$*%_";
  privmsg = "%c[%m$0%c(%m{nickhost $1-}%c)]%n ";
  ownprivmsg = "%c[%Yquery%c(%y$1-%c)]%n ";
  ownprivmsgnick = "%c<%y$*%n%c>%N ";
  ownprivnick = "%_$*%_";
  privmsgnick = "%c<%m$*%n%c>%N ";

  # Actions
  action = "%M*%n";
  ownaction = "{action } %c$0 $1-";
  pvtaction = "%c[%mquery%c(%m$0%c)]%n {action } %m->%n $1-";
  pvtaction_query = "%c* $* {action } ";
  pubaction = "{action } %c$* } %m";

  # System Messages
  mode = "%c<%m%_$*%c%_%c>%N";
  ban = "$*";
  netsplit = "%M$*";
  netjoin = "%M$*";
  names_nick = "%m$0%w$[9]1-%n ";
  names_users = "(%m$0%w(%m$1%W))";
  names_channel = "%m$*";
  dcc = "%m$0%n $1 $3 $4 %c$5 $6 $7 $8-%n";
  dccfile = "%_$*%_";
  dccownmsg = "%c[%mdcc%n(%m$*%n)]%n ";
  dccownnick = "%c$*%n";
  dccownaction = "%c[%mdcc%m(%m$0%n)]%n {action } %w->%n $1-";
  dccmsg = "%c[%mdcc%n(%m$*%n)]%n ";
  dccquerynick = "%m$*%n";
  dccaction = "%c[%mdcc%n(%m$*)]%n {action }%n %|";

  # Whois
  whois = "$[12]0 : $1-";
  whois1 = "$[12]0 : $1-";
  whois2 = "$[12]0 : $1-";
  whois3 = "$[12]0 : $1-";

  # Notices
  ownnotice = "%c[%n%mnotice%n%c(%m$1-%n%c)]%n ";
  notice = "%M❯ %mNotice %c>%m>%c> %n{nick $0}: $1";
  pubnotice_channel = ":$*";
  pvtnotice_host = "%c(%n%m$*%n%c)%n";
  servernotice = "{notice $*}";

  # CTCP
  ownctcp = "%c[%N%m$0%n%c(%m$1-%n%c)]%n ";
  ctcp = "%c>%n>%m>%n {nick $0} %m$1%n $2 %c$3%n $4 $5 %m$6%n";

  # Wallops
  wallop = " $*: ";
  wallop_nick = " $*";
  wallop_action = " * $* ";

  # Status Bar
  sb_background = "%m";
  sb_topic_bg = "%M";
  sb = "%c· %n$0-%c ·%n";
  prompt = "%M❯%n ";
  sbmode = "$0-";
  sbaway = " %Y(away)%n";
  sbservertag = ":$0 %n(%cchange with ^X%n)";
  sbmore = "  %r<%R<%k< %nmore %k>%R>%r>  ";
  sblag = "{sb Lagging %r$0-%K seconds!}";
  sb_default_bg = "%5";
  sb_act_sep = "%c/%n";
  sb_act_text = "%M$*";
  sb_act_msg = "%c$*";
  sb_act_hilight = "%Y$*";
  sb_act_hilight_color = "%m$0$1-%n";
  sb_info1_bg = "%m";
  sb_window_bg = "%m%W";
  sb_window2_bg = "%m%W";
  sb_info2_bg = "%m";
  sb_usercount = "{sb %cN%wetwork:%n %K$tag }{sb %cU%wsers: %R$0 %K$1-";
  sb_uc_normal = "%cN%wormal %Y$*%R]";
  sb_uc_ops = "%R[%cIRC%wOpers %_%y$mh_opercount  ·  %cO%wpers %_%Y$*%c  · ";
  sb_uc_voices = "%cV%woice %Y$*%c  · ";

  # nm2 script settings
  nickalign = "";
  nickcolor = "%n";
  nicktrunc = "";
  cumode_space = " ";

  # Colors
  # %m, %M - magenta, bold magenta (neon pink)
  # %c, %C - cyan, bold cyan (electric cyan)
  # %y, %Y - yellow, bold yellow (gold)
  # %r, %R - red, bold red (errors)
  # %k, %K - black
  # %w, %W - white
  # %b, %B - blue (deep purple for background)
  # %n - default
};

formats = {
  "fe-common/core" = {
    join = "%M⊕%n {channick_hilight %c$0} {chanhost_hilight %c$1}";
    part = "%Y⊖%n {channick_hilight %y$0} {chanhost_hilight %K$1} %c{reason $3}";
    kick = "%R⊗%n {channick_hilight %m$0} by {nick $2} %c{reason $3}";
    quit = "%r⏻%n {channick_hilight %y$0}%n {chanhost_hilight %K$1}%n %c{reason $2}";
    notice = "{notice $0 $1}";
    quit_once = "{channel $3} {channick $0} {chanhost $1} {reason $2}";
    servertag = "%m[%W$0%m] ";
    invite = "%c✉%n {nick %m$0} to {channel %c$1}";
    new_topic = "%c✍️ %nTopic was changed by %m{channick $0} in {channel %c$1} to: $2";
    topic_unset = "%c💨 %nTopic for %n{channel %c$1} %Wunset by %n{channick %m$0}";
    your_nick_changed = "👤 You are now %Y{nick $1} · ";
    nick_changed = "👤 {channick %m$0} %wis now %Y{%channick_hilight $1}";
    talking_in = "🗣️ You are now talking in {channel %c$0}";
    not_in_channels = "🤷 You are not on any channels";
    names = "{names_users %W · Users · ) (channel {names_channel $0}} · (%M$1) · ";
    names_nick = "{names_nick $0 $1}";
    endofnames = "{channel %w$0}: %WTotal of {hilight %m((%M$1%R))%W} %wnicks%n {comment %WOps {hilight %W((%M$2%W))%W}, Voice %m(({hilight %M$4%R))%W}, Normal %m(({hilight %M$5%R))%W}";

    pubmsg = "{pubmsgnick $nickalign$2 {pubnick $nickcolor$0$nicktrunc}}$1";
    own_msg = "{ownmsgnick $nickalign$2 {ownnick $nickcolor$0$nicktrunc}}$1";
    own_msg_channel = "{ownmsgnick $nickalign$3 {ownnick $nickcolor$0$nicktrunc}{msgchannel $1}}$2";
    pubmsg_me = "{pubmsgmenick $nickalign$2 {menick $0$nicktrunc}}$1";
    pubmsg_me_channel = "{pubmsgmenick $nickalign$3 {menick $0$nicktrunc}{msgchannel $1}}$2";
    pubmsg_hilight = "{pubmsghinick $0 $nickalign$3 $1$nicktrunc}$2";
    pubmsg_hilight_channel = "{pubmsghinick $0 $nickalign$4 $1$nicktrunc{msgchannel $2}}$3";
    pubmsg_channel = "{pubmsgnick $nickalign$3 {pubnick $nickcolor$0$nicktrunc}{msgchannel $1}}$2";
    own_msg_private_query = "{ownprivmsgnick $nickalign{ownprivnick $nickcolor$2$nicktrunc}}$1";
    msg_private_query = "{privmsgnick $nickalign$nickcolor$0$nicktrunc}$2";
  };
  "fe-common/irc" = {
    inviting = "%c✉%n Inviting {nick %m$0} to {channel %c$1}";
    topic_info = "%c📜 %nTopic set by %N{channick %m$0} {comment $1}";
    server_chanmode_change = "{netsplit %CServerMode%n}/{channelhilight $0} {mode $1} by {nick $2}";
    whois = "{nick $0} ({nickhost $1%R@%n$2}) {comment $4}%:{whois %_i%_rcname $3}";
    whowas = "           %WWHOWAS%K       %n%:%r*%n {nick $0} ({nickhost $1%R@%n$2})%:{whois %_i%_rcname $3}";
    whois_idle = "{whois3 %_i%_dle %|$1 days $2 hours $3 mins $4 secs}";
    whois_idle_signon = "{whois3 %_i%_dle %|$1 days $2 hours $3 mins $4 secs (signon: $5)}";
    whois_server = "{whois2 %_s%_erver %|$1 ($2)}";
    whois_oper = "{whois3 %_o%_perator {hilight $1}}";
    whois_registered = "{whois %_a%_uth has registered this nick}";
    whois_help = "{whois %_d%_uty is available for help}";
    whois_modes = " {whois %_m%_odes $1}";
    whois_realhost = "{whois %_h%_ostname $1-}";
    whois_usermode = "{whois %_u%_sermode $1}";
    whois_channels = "{whois %_c%_hannels %|$1}";
    whois_away = "{whois %_a%_way %W%|$1}";
    whois_special = "{whois  %|{hilight $1}}";
    end_of_whois = "%WEnd of WHOIS · ";
    end_of_whowas = "%wEnd of WHOWAS · ";
    whois_not_found = "%wThere is no such Nick:%R.%w {nick %W$0}";
    who = "{channelhilight %C$[!10]0%n} %|{nick $[!9]1} %c$[!3]2%n $[!2]3 $4%R@%n$5 :: $6";
    ctcp_requested = "requested $2 from {nick $3} {ctcp {hilight $0} {comment $1}}";
    own_action = "$nickalign{ownaction $0$nicktrunc}$1";
    action_private = "$nickalign{pvtaction $0$nicktrunc}$2";
    action_private_query = "$nickalign{pvtaction_query $0$nicktrunc}$2";
    action_public = "$nickalign{pubaction $0$nicktrunc}$1";
  };
  "fe-common/irc/dcc" = {
    own_dcc = "{dccownmsg {dccownnick $1}}$2";
    own_dcc_action = "{dccownaction_target $0 $1}$2";
    own_dcc_action_query = "{dccownaction $0}$2";
    own_dcc_ctcp = "{ownctcp ctcp $0}$1 $2";
    dcc_msg = "{dccmsg $0}$1";
    action_dcc = "{dccaction $0}$1";
    action_dcc_query = "{dccaction $0}$1";
    own_dcc_query = "{dccownmsg {dccownnick $0}}$2";
    dcc_msg_query = "{dccmsg $0}$1";
    dcc_ctcp = "{dcc %W>>> DCC CTCP {hilight $1} received from {hilight $0}: $2}";
  };
  nm2 = { neat_pad_char = " "; };
  "Irssi::Script::adv_windowlist" = {
    awl_display_key_active = "%M❯ $N %c$H$C$S";
    awl_display_key = "  %m$N %c$H$C$S";
    awl_viewer_item_bg = "%N";
    awl_display_header = "";
    awl_display_nokey = "  %m$N %c$H$C$S";
    awl_display_nokey_visible = "· %m$N %c$H$C$S";
    awl_display_key_visible = "· %m$N %c$H$C$S";
    awl_display_nokey_active = "%M❯ $N %c$H$C$S";
    awl_data_level_none = "%c";
    awl_data_level_low = "%y";
    awl_data_level_medium = "%Y";
    awl_data_level_high = "%R";
  };
  "Irssi::Script::nm2" = { neat_pad_char = " "; };
};
