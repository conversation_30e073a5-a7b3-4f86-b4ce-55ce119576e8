replaces = { ":()" = "%K$*%n"; };
abstracts = {
  line_start = "%R.:";
  timestamp = "%W.:%C$*%W:.%n";
  hilight = "%W$*";
  error = "%r$*%n";
  channel = "%R%_$*%_";
  channel2 = "%W$C%n";
  nick = "%n$*%n";
  nickhost = "$*";
  server = "%c$*%N";
  comment = "%W[%n$*%W]%N";
  reason = "%W(%n$*%K)%N";
  mode = "%W<%R%_$*%W%_%W>%N";
  channick_hilight = "%B$*%n";
  channick_hilight2 = "%W$*%n";
  chanhost_hilight = "%w<{nickhost $*}%w>";
  channick = "%c$*%n";
  chanhost = "%W<%n%c{nickhost $*}%n%W>%n";
  channelhilight = "%c$*%n";
  ban = "$*";
  msgnick = "%n%K$0%W$1-%_%W:%n %|";
  ownmsgnick = "%n%K$0%W$1-%_%W:%n %|";
  ownnick = "%y%_$*%_";
  pubmsgnick = "{msgnick $0 $1-}";
  pubnick = "%_$*%_";
  pubmsgmenick = "{msgnick $0 %_$1-%_%n}";
  menick = "%R$*%n";
  pubmsghinick = "{msgnick $1 $0$2-}";
  msgchannel = "%W:%n%_$*%_";
  privmsg = "%K[%n%P$0%n%K(%n{nickhost $1-}%p%n%K)]%n ";
  ownprivmsg = "%K[%n%c%n%K(%C$1-%n%K)]%n ";
  ownprivmsgnick = "%W<%b$*%n%W>%N ";
  ownprivnick = "%_$*%_";
  privmsgnick = "%W<%_%C$*%n%W>%N ";
  action = "%K*%n";
  ownaction = "{action } %W$0 $1-";
  pvtaction = "%K[%cquery%n(%C$0%n)]%n {action } %w->%n $1-";
  pvtaction_query = "%W* $* {action } ";
  pubaction = "{action } %W$* } %g";
  whois = "$[12]0 : $1-";
  whois1 = "$[12]0 : $1-";
  whois2 = "$[12]0 : $1-";
  whois3 = "$[12]0 : $1-";
  ownnotice = "%K[%n%cnotice%n%K(%C$1-%n%K)]%n ";
  notice = "%K-%n%C$0%n%K-%n ";
  pubnotice_channel = ":$*";
  pvtnotice_host = "%K(%n%c$*%n%K)%n";
  servernotice = "{notice $*}";
  ownctcp = "%K[%N%c$0%n%K(%C$1-%n%K)]%n ";
  ctcp = "%K>%n>%W>%n {nick $0} %g$1%n $2 %W$3%n $4 $5 %g$6%n";
  wallop = " $*: ";
  wallop_nick = " $*";
  wallop_action = " * $* ";
  netsplit = "%W$*";
  netjoin = "%W$*";
  names_nick = "%B$0%w$[9]1-%n ";
  names_users = "(%c$0%w(%B$1%W))";
  names_channel = "%B$*";
  dcc = "%C$0%n $1 $3 $4 %c$5 $6 $7 $8-%n";
  dccfile = "%_$*%_";
  dccownmsg = "%K[%gdcc%n(%C$*%n)]%n ";
  dccownnick = "%c$*%n";
  dccownaction = "%K[%gdcc%g(%C$0%n)]%n {action } %w->%n $1-";
  dccmsg = "%K[%cdcc%n(%C$*%n)]%n ";
  dccquerynick = "%C$*%n";
  dccaction = "%K[%gdcc%n(%g$*)]%n {action }%n %|";
  sb_background = "%0";
  sb_topic_bg = "%0%W";
  sb = "%W.:%n$0-%W:.%n";
  prompt = "%K<%n%_$0%n%K>%n ";
  sbmode = "$0-";
  #sbaway = ":%K.%W: %WAway:%n %K'%c$A%K'%n";
  sbservertag = ":$0 %n(%cchange with ^X%n)";
  sbmore = "  %r<%R<%k< %nmore %k>%R>%r>  ";
  sblag = "{sb Lagging %r$0-%K seconds!}";
  sb_default_bg = "%4";
  sb_act_sep = "%K/";
  sb_act_text = "%R$*";
  sb_act_msg = "%W$*";
  sb_act_hilight = "%M$*";
  sb_act_hilight_color = "$0$1-%n";
  sb_info1_bg = "%n";
  sb_window_bg = "%r%W";
  sb_window2_bg = "%r%W";
  sb_info2_bg = "%n";

  sb_usercount = "{sb %WN%wetwork:%n %K$tag }{sb %WU%wsers: %R$0 %K$1-";
  sb_uc_normal = "%WN%wormal %Y$*%R]";
  sb_uc_ops = "%R[%WIRC%wOpers %_%y$mh_opercount :.: %WO%wpers %_%Y$*%c :.:";
  sb_uc_voices = "%WV%woice %Y$*%c :.:";

};
formats = {
  "fe-common/core" = {
    join = "%Kmode   %Wjoin      %K>%g>%G> %n{channick_hilight %W$0} {chanhost_hilight %w$1}";
    part = "%Kmode   %wpart      %W<%r<%R< %n{channick_hilight2 %w$0} {chanhost_hilight %K$1} %W{reason $3}";
    kick = "%Kmode   %RKICK      %R<%r<%K< %n{channick_hilight2 %W$0} by {nick $2} %W{reason $3}";
    quit = "%Kmode   %RKILLED    %R<<< %n{channick_hilight2 %w$0}%n {chanhost_hilight %K$1}%n %W{reason $2}";
    quit_once = "{channel $3} {channick $0} {chanhost $1} {reason $2}";
    servertag = "%b[%W$0%b] ";
    invite = "%gInvites %K>%r>%R> %n{nick $0} to {channel $1}";
    new_topic = "%Kmode   %R#      %K>%r>%R> %nTopic was changed by %W{channick $0} in {channel $1} to: $2";
    topic_unset = "%Kmode   %R#   %R<%r<%K< %nTopic for %n{channel $1} %Wunset by %n{channick $0}";
    your_nick_changed = "You're Nickname %nis now %W{nick $1}:.";
    nick_changed = "%Kmode   ~   {channick %W$0} %wis now %W{%Wchannick_hilight %W$1}";
    talking_in = "%G You are now talking in {%Rc%Whannel $0}";
    not_in_channels = "%R You are not on any %Rc%Whannels";
    names = "{names_users %W.:Users:.) (channel {names_channel $0}}.:(%G$1):.";
    names_nick = "{names_nick $0 $1}";
    endofnames = "{channel %w$0}: %WTotal of {hilight %R((%G$1%R))%W} %wnicks%n {comment %WOps {hilight %W((%G$2%W))%W}, Voice %R(({hilight %G$4%R))%W}, Normal %R(({hilight %G$5%R))%W}";

    pubmsg = "{pubmsgnick $2 {pubnick $0}}$1";
    own_msg = "{ownmsgnick $2 {ownnick $0}}$1";
    own_msg_channel = "{ownmsgnick $3 {ownnick $0}{msgchannel $1}}$2";
    pubmsg_me = "{pubmsgmenick $2 {menick $0}}$1";
    pubmsg_me_channel = "{pubmsgmenick $3 {menick $0}{msgchannel $1}}$2";
    pubmsg_hilight = "{pubmsghinick $0 $3 $1}$2";
    pubmsg_hilight_channel = "{pubmsghinick $0 $4 $1{msgchannel $2}}$3";
    pubmsg_channel = "{pubmsgnick $3 {pubnick $0}{msgchannel $1}}$2";
  };
  "fe-common/irc" = {
    inviting = "%K>%w>%W>%n Inviting {nick $0} to {channel $1}";
    topic_info = "%Kmode   %nTopic set by %N{channick $0} {comment $1}";
    server_chanmode_change = "{netsplit %CServerMode%n}/{channelhilight $0} {mode $1} by {nick $2}";
    #whois = "%b- %C--%W+%C-----%W.%coOo%W,%C--<%C(%mxl%C)%C>--%W,%coOo%W.%C----------%W+%C-- %b----%n%:%C*%n {nick $0} ({nickhost $1%R@%n$2}) {comment $4}%:{whois %_i%_rcname $3}";
    whois = "{nick $0} ({nickhost $1%R@%n$2}) {comment $4}%:{whois %_i%_rcname $3}";
    whowas = "           %WWHOWAS%K       %n%:%r*%n {nick $0} ({nickhost $1%R@%n$2})%:{whois %_i%_rcname $3}";
    whois_idle = "{whois3 %_i%_dle %|$1 days $2 hours $3 mins $4 secs}";
    whois_idle_signon = "{whois3 %_i%_dle %|$1 days $2 hours $3 mins $4 secs (signon: $5)}";
    whois_server = "{whois2 %_s%_erver %|$1 ($2)}";
    whois_oper = "{whois3 %_o%_perator {hilight $1}}";
    whois_registered = "{whois %_a%_uth has registered this nick}";
    whois_help = "{whois %_d%_uty is available for help}";
    whois_modes = " {whois %_m%_odes $1}";
    whois_realhost = "{whois %_h%_ostname $1-}";
    whois_usermode = "{whois %_u%_sermode $1}";
    whois_channels = "{whois %_c%_hannels %|$1}";
    #    whois_away = "{whois %_a%_way %|$1}";
    whois_away = "{whois %_a%_way %W%|$1}";
    whois_special = "{whois  %|{hilight $1}}";
    end_of_whois = "%WEnd of WHOIS:.";
    end_of_whowas = "%wEnd of WHOWAS:.";
    whois_not_found = "%wThere is no such Nick:%R.%w {nick %W$0}";
    who = "{channelhilight %C$[!10]0%n} %|{nick $[!9]1} %c$[!3]2%n $[!2]3 $4%R@%n$5 :: $6";
    ctcp_requested = "requested $2 from {nick $3} {ctcp {hilight $0} {comment $1}}";

  };
  "fe-common/irc/dcc" = {
    own_dcc = "{dccownmsg {dccownnick $1}}$2";
    own_dcc_action = "{dccownaction_target $0 $1}$2";
    own_dcc_action_query = "{dccownaction $0}$2";
    own_dcc_ctcp = "{ownctcp ctcp $0}$1 $2";
    dcc_msg = "{dccmsg $0}$1";
    action_dcc = "{dccaction $0}$1";
    action_dcc_query = "{dccaction $0}$1";
    own_dcc_query = "{dccownmsg {dccownnick $0}}$2";
    dcc_msg_query = "{dccmsg $0}$1";
    dcc_ctcp = "{dcc >>> DCC CTCP {hilight $1} received from {hilight $0}: $2}";
  };
  "Irssi::Script::country" = {
    whois = "%b- %C--%W+%C-----%W.%coOo%W,%C--<%C(%WA%C)_(%WA%C)%C>--%W,%coOo%W.%C----------%W+%C-- %b----%n%:%C*%n {nick $0} ({nickhost $1%R@%n$2}) {comment $4}%:{whois %_i%_rcname $3}";
    whowas = "%b.%B.%C...........................................%n%:%G|%n {nick $0} ({nickhost $1%R@%n$2}) {comment $4}%:{whois %_i%_rcname $3}";
  };
};
