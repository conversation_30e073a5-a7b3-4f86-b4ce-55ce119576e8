# Dokumentacja Skryptów Autorun

*Szczegółowa analiza skryptów automatycznie ładowanych w irssi*

---

## Przegląd Skryptów Autorun

Katalog `/scripts/autorun/` zawiera 17 skryptów automatycznie ładowanych przy starcie irssi:

### Skrypty UI i Wizualne
1. **adv_windowlist.pl** - Zaawansowana lista okien (AWL)
2. **nm2.pl** - Wyrównywanie nicków
3. **nickcolor_expando_simple.pl** - Kolorowanie nicków
4. **rotator.pl** - Animowany wskaźnik aktywności
5. **mh_sbuserinfo.pl** - Informacje o użytkownikach w statusbar
6. **mh_windowfill.pl** - Wypełnianie okien
7. **tmux-nicklist-portable.pl** - Lista nicków w tmux

### Skrypty Funkcjonalne
8. **spellcheck.pl** - Sprawdzanie pisowni
9. **translate.pl** - Tłumaczenie wiadomości
10. **autocycle.pl** - Automatyczne cyklowanie
11. **autorejoin.pl** - Automatyczne dołączanie do kanałów
12. **chansort_configurable.pl** - Sortowanie kanałów
13. **keepnick.pl** - Utrzymywanie nicka
14. **mkick.pl** - Masowe kickowanie
15. **mouse.pl** - Obsługa myszy
16. **scriptassist.pl** - Asystent skryptów
17. **test.pl** - Skrypt testowy

---

## Szczegółowa Analiza Kluczowych Skryptów

### 1. Advanced Window List (adv_windowlist.pl)

**Wersja**: 1.11  
**Autor**: Nei  
**Funkcja**: Zaawansowana lista okien w statusbar lub zewnętrznym panelu

#### Kluczowe Funkcjonalności
- Wyświetlanie listy okien w statusbar lub tmux split
- Wskaźniki aktywności z kolorami
- Obsługa hotkeys i numerów okien
- Integracja z motywem Nexus Steel

#### Konfiguracja w Motywie
```irssi
"Irssi::Script::adv_windowlist" = {
  awl_display_key_active = "%B⚡%N$N%b${cumode_space}%G$H$C$S";
  awl_display_key = "  %w$N%k${cumode_space}%w$H$C$S";
  awl_display_nokey_active = "%B⚡%N$N%b${cumode_space}%G$H$C$S";
  awl_data_level_high = "%R";    # Crimson red dla wysokiej aktywności
  awl_data_level_medium = "%Y";  # Amber yellow dla średniej aktywności
  awl_data_level_low = "%b";     # Sky blue dla niskiej aktywności
  awl_data_level_none = "%w";    # Silver gray dla braku aktywności
};
```

#### Integracja z Nexus Steel
- Używa ikony ⚡ (power symbol) dla aktywnych okien
- Paleta kolorów zgodna z motywem cyberpunk
- UTF-8 wskaźniki aktywności

---

### 2. Nick Alignment (nm2.pl)

**Wersja**: 2.1  
**Autor**: Nei  
**Funkcja**: Wyrównywanie nicków w wiadomościach

#### Kluczowe Funkcjonalności
- Dynamiczne wyrównywanie nicków do prawej strony
- Inteligentne skracanie długich nicków
- Wsparcie dla kolorowania nicków
- Konfigurowalny padding i długość

#### Ustawienia w Konfiguracji
```irssi
neat_maxlength = "15";
neat_dynamic = "yes";
neat_colors = "1";
neat_shrink_uniq = "1";
neat_colorize = "1";
neat_allow_shrinking = "1";
neat_melength = "15";
```

#### Integracja z Motywem
```irssi
"Irssi::Script::nm2" = {
  neat_pad_char = " ";
  neat_left_actions = "0";
  neat_right_actions = "1";
  neat_left_messages = "0";
  neat_right_messages = "1";
};
```

---

### 3. Nick Color Expando (nickcolor_expando_simple.pl)

**Wersja**: 0.0.2  
**Autor**: Nei  
**Funkcja**: Proste kolorowanie nicków oparte na hash

#### Kluczowe Funkcjonalności
- Hash-based kolorowanie nicków
- Expando $nickcolor dla motywów
- Wsparcie dla 16 kolorów
- Integracja z nm2

#### Konfiguracja Kolorów
```irssi
nick_colors = "%r %R %g %G %y %b %B %c %C %X42 %X3A %X5E %X4N %X3H %X3C %X32";
nick_char_sum_hash = "yes";
```

#### Paleta Nexus Steel
- 16 kolorów zgodnych z motywem cyberpunk
- Extended colors (%X) dla większej różnorodności
- Consistent hashing dla stabilnych kolorów

---

### 4. Rotator (rotator.pl)

**Wersja**: 0.2.1  
**Autor**: BC-bd  
**Funkcja**: Animowany wskaźnik aktywności w statusbar

#### Kluczowe Funkcjonalności
- Animowane znaki pokazujące że irssi działa
- Konfigurowalny separator i znaki
- Bounce mode (odbijanie) lub loop mode
- Regulowana prędkość animacji

#### Konfiguracja Nexus Steel
```irssi
rotator_chars = "%B%W⚡%k________%k %k_%k%W⚡%k_______%k %k__%k%W⚡%k______%k %k___%k%W⚡%k_____ %k____%k%W⚡%k____%k %k_____%G%W⚡%k___%k %k______%k%W⚡%k__%k %k_______%k%W⚡%k_%k %k________%k%W⚡%k";
rotator_speed = "180";
```

#### Animacja
- Używa ikony ⚡ (lightning bolt) jako głównego elementu
- Cyberpunk styling z podkreśleniami
- Gradient kolorów %B→%W→%G (blue→white→green)
- 180ms interval dla płynnej animacji

---

### 5. Statusbar User Info (mh_sbuserinfo.pl)

**Wersja**: 1.06  
**Autor**: Michael Hansen  
**Funkcja**: Informacje o użytkownikach kanału w statusbar

#### Kluczowe Funkcjonalności
- Liczba użytkowników (ops, voice, normal)
- Informacje o limicie kanału
- Ostrzeżenia o braku operatorów
- Kolorowe wskaźniki statusu

#### Format Wyświetlania
```
[Users: <users>(*<users_oper>:@<users_op>:+<users_voice>:<users_rest>)/<limit>(<limitusers>)]
```

#### Konfiguracja
- Prefix: "Users: "
- Separator: ":"
- Divider: "/"
- Group brackets: "()"
- Mode prefixes: *@%+

---

### 6. Tmux Nicklist (tmux-nicklist-portable.pl)

**Wersja**: 0.1.8  
**Autor**: Thiago de Arruda  
**Funkcja**: Lista nicków w osobnym panelu tmux

#### Kluczowe Funkcjonalności
- Pionowy panel z listą nicków (20% szerokości)
- Obsługa myszy i klawiatury
- Kolorowanie nicków (wymaga nickcolor script)
- Sortowanie away users na dole
- Regex filtrowanie kanałów

#### Konfiguracja
```irssi
nicklist_channel_re = ".*";      # Regex dla kanałów
nicklist_max_users = "0";        # Max users (0 = zawsze)
nicklist_smallest_main = "0";    # Min wielkość głównego okna
nicklist_pane_width = "20";      # Szerokość panelu
nicklist_color = "ON";           # Kolorowanie nicków
nicklist_gone_sort = "ON";       # Sortowanie away users
```

#### Klawisze Nawigacji
- k/↑: w górę o jedną linię
- j/↓: w dół o jedną linię
- u/PageUp: w górę o 50%
- d/PageDown: w dół o 50%
- gg: na górę
- G: na dół

---

### 7. Spellcheck (spellcheck.pl)

**Wersja**: 0.9.1  
**Autorzy**: Jakub Wilk, Jakub Jankowski, Gabriel Pettier, Nei  
**Funkcja**: Sprawdzanie pisowni używając Aspell

#### Kluczowe Funkcjonalności
- Real-time sprawdzanie pisowni podczas pisania
- Wsparcie dla wielu języków jednocześnie
- Kolorowe podkreślanie błędów
- Sugestie poprawek
- Inteligentne pomijanie URL, email, ścieżek

#### Konfiguracja
```irssi
spellcheck_default_language = "pl_PL";
spellcheck_print_suggestions = "no";
spellcheck_word_color = "%R";           # Czerwony dla błędów
spellcheck_word_input_color = "%U%R";   # Podkreślony czerwony w input
```

#### Wsparcie Języków
- Domyślny: Polski (pl_PL)
- Możliwość dodania wielu języków: "pl_PL+en_US"
- Fast suggestion mode dla wydajności

---

### 8. Translate (translate.pl)

**Wersja**: 1.1  
**Autor**: Jerzy (kofany) Dabrowski  
**Funkcja**: Tłumaczenie wiadomości używając Google Translate API

#### Kluczowe Funkcjonalności
- Automatyczne tłumaczenie wiadomości przychodzących
- Tłumaczenie wiadomości wychodzących
- Detekcja języka źródłowego
- Per-channel/user konfiguracja
- Komenda /tr dla szybkiego tłumaczenia

#### Konfiguracja
```irssi
translate_api_key = "AIzaSyDUfcmoiserVvtS_jlQ7Tj9s7xM-6Fyti0";
translate_list_in = "{\"#ditalinux\":{\"target_lang\":\"pl\",\"source_lang\":\"it\"}}";
translate_list_out = "{\"gosub\":{\"target_lang\":\"en\",\"source_lang\":\"pl\"}}";
translate_print_original = "yes";
translate_scrollback_lines = "3";
```

#### Komendy
- `/tr <lang> <text>` - Szybkie tłumaczenie
- `/translate addin <target> <source_lang> <target_lang>` - Dodaj tłumaczenie przychodzące
- `/translate addout <target> <source_lang> <target_lang>` - Dodaj tłumaczenie wychodzące
- `/translate list` - Lista konfiguracji
- `/translate save` - Zapisz konfigurację

---

### 9. Pozostałe Skrypty

#### autorejoin.pl
- Automatyczne dołączanie do kanałów po kick/ban
- Konfigurowalny delay: `autorejoin_delay = "10"`

#### chansort_configurable.pl
- Automatyczne sortowanie kanałów
- `chansort_configurable_autosort = "yes"`

#### keepnick.pl
- Utrzymywanie preferowanego nicka
- Automatyczne odzyskiwanie po zmianie

#### mkick.pl
- Masowe kickowanie użytkowników
- Domyślny powód: `masskick_default_reason = ".: tahioN :."`

#### scriptassist.pl
- Asystent zarządzania skryptami
- Automatyczne aktualizacje i instalacja

---

## Integracja Skryptów z Motywem Nexus Steel

### Kolorystyka
- **Aktywność wysoka**: %R (crimson-red)
- **Aktywność średnia**: %Y (amber-yellow)  
- **Aktywność niska**: %b (sky-blue)
- **Brak aktywności**: %w (silver-gray)
- **Elementy aktywne**: %B⚡ (electric-blue lightning)

### UTF-8 Ikony
- ⚡ - Power symbol (aktywne okna, rotator)
- ◆ - Component symbol (widoczne okna)
- ⇋ - Exchange symbol (user info)
- ➜ - Arrow symbol (navigation)

### Statusbar Integration
Wszystkie skrypty UI integrują się z niestandardowym statusbar:
- **info1**: user, rotator, window
- **info2**: time, more, upgradeinfo, act, lag

---

*Dokumentacja utworzona na podstawie analizy skryptów w `/Users/<USER>/.irssi/scripts/autorun/`*
