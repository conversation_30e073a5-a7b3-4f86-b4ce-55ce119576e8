set -g default-terminal "tmux-256color"
#set -g status off
#set -g pane-border-style fg=colour235,bg=black
#set -g pane-active-border-style fg=colour235,bg=black
set -g prefix C-a
unbind-key C-b
bind-key C-a send-prefix

set-environment -g PATH /usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin

set -g allow-passthrough on
set -g history-limit 49152
set -g base-index 1
set -g pane-base-index 1
set -g renumber-windows on
set -s escape-time 0
set -g status-keys vi
set -g mode-keys vi
set -g mouse on
set -g aggressive-resize on
set -g focus-events on

set -g status-position top
set -g status-justify left
set -g status-left-length 40
set -g status-right-length 80

set -g pane-border-style "fg=#14181d"
set -g pane-active-border-style "fg=#65b1cd"
set -g mode-style "bg=#191726,fg=#65b1cd"
set -g message-style "bg=#191726,fg=#f6c177,bold"
set -g message-command-style "fg=#191726,bg=#f6c177,bold"

setw -g window-status-activity-style "underscore,fg=#6e6a86,bg=default"
setw -g window-status-separator ""
setw -g window-status-style "fg=#f6c177,bg=default"
setw -g window-status-format "#[fg=#6e6a86,bg=default,nounderscore] #I #W #F"
setw -g window-status-current-format "#[fg=#65b1cd,bg=default,bold] #I [#W] #F"

set -g status-style "bg=default,fg=#6e6a86"
set -g status-left '#[fg=#14181d,bg=#65b1cd,bold] #S #[fg=#65b1cd,bg=default]'
STATUSB='#(/usr/local/bin/tmux-mem-cpu-load -g 0 --interval 2) #[fg=#65b1cd,bg=default]#[fg=#14181d,bg=#65b1cd,bold] %H:%M'

bind-key x kill-pane
bind-key r source-file ~/.tmux.conf \; display-message "tmux.conf reloaded"
bind-key v split-window -c "#{pane_current_path}"
bind-key b split-window -h -c "#{pane_current_path}"

bind-key H resize-pane -D 18

bind-key h select-pane -L
bind-key j select-pane -D
bind-key k select-pane -U
bind-key l select-pane -R

# Szybkie przełączanie okien Alt+strzałki
bind -n M-Left previous-window
bind -n M-Right next-window

# Szybkie kopiowanie do schowka (macOS)
bind-key -T copy-mode-vi y send-keys -X copy-pipe-and-cancel "pbcopy"

set -g status-interval 60
WEATHER='#(curl -s wttr.in/Jarosław\?format\="%%l:+%%c+%%t+%%f+%%h+%%p+%%P+%%m+%%w+%%S+%%s")'
set -g status-right "$WEATHER $STATUSB #[fg=#65b1cd] %d-%m-%Y"

# Wyłączenie dźwięków (bell)
set -g bell-action none