# Directory Overview

This directory contains the configuration and data for the Irssi IRC client. It is not a software project but rather a user-specific configuration for an application.

## Key Files and Directories

*   **`config`**: The main configuration file for Irssi. It defines servers, chat networks, aliases, keyboard bindings, and other settings.
*   **`*.theme`**: These files define the visual appearance of the Irssi client. The active theme is `tahio.theme`.
*   **`scripts/`**: This directory contains Perl scripts (`.pl`) that extend Irssi's functionality.
    *   **`scripts/autorun/`**: Scripts in this directory are automatically loaded when Irs<PERSON> starts.
*   **`logs/`**: This directory contains chat logs from various IRC networks and channels.
*   **`nicklist-*`**: These are temporary files used by Irssi.

## Usage

This directory is used by the Irssi client to store its configuration and data. To use this configuration, you would typically start the `irssi` command-line client. The client will automatically load the settings from the `config` file and the scripts from the `scripts/autorun/` directory.

The user is active on the following IRC networks:

*   IRCnet
*   belwue
*   fu-berlin

And the following channels:

*   #polska
*   #tahio
