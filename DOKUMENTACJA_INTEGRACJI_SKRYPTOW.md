# Dokumentacja Integracji Skryptów

*Szczegółowy opis współpracy skryptów z motywem Nexus Steel i konfiguracją irssi*

---

## Przegląd Integracji

Konfiguracja irssi tworzy spójny ekosystem, gdzie skrypty autorun, motyw Nexus Steel, statusbar i ustawienia współpracują ze sobą, tworząc nowoczesny cyberpunk interface. Każdy element jest precyzyjnie dostrojony do pozostałych.

---

## 1. Integracja AWL (Advanced Window List)

### Konfiguracja w Motywie
```irssi
"Irssi::Script::adv_windowlist" = {
  # Aktywne okno z ikoną power symbol
  awl_display_key_active = "%B⚡%N$N%b${cumode_space}%G$H$C$S";
  
  # Nieaktywne okna w steel gray
  awl_display_key = "  %w$N%k${cumode_space}%w$H$C$S";
  
  # Widoczne okna z diamond indicator
  awl_display_nokey_visible = "%C◆%N %w$N %b${cumode_space}%C$H%N$C$S";
  
  # Poziomy aktywności zgodne z paletą Nexus Steel
  awl_data_level_none = "%w";    # Silver-gray
  awl_data_level_low = "%b";     # Sky-blue  
  awl_data_level_medium = "%Y";  # Amber-yellow
  awl_data_level_high = "%R";    # Crimson-red
};
```

### Integracja z Statusbar
AWL jest zintegrowane z statusbar poprzez ustawienia w config:
```irssi
statusbar = {
  default = {
    info2 = {
      items = {
        act = { };  # AWL activity display
      };
    };
  };
};
```

### Współpraca z Aliasami
Aliasy zarządzania oknami współpracują z AWL:
```irssi
WIN1 = "window 1";     # Przełączanie okien
WINDOWLIST = "window list";  # Lista okien
CLEARALL = "foreach window command clear";  # Czyszczenie wszystkich
```

---

## 2. Integracja nm2 (Nick Alignment)

### Konfiguracja w Motywie
```irssi
"Irssi::Script::nm2" = {
  neat_pad_char = " ";
  neat_maxlength = "15";
  neat_colors = "1";
  neat_shrink_uniq = "1";
  neat_colorize = "1";
  neat_allow_shrinking = "1";
  neat_melength = "15";
};
```

### Integracja z Message Formats
nm2 zmienia formaty wiadomości w motywie:
```irssi
# Przed nm2
pubmsg = "{pubmsgnick $2 {pubnick $0}}$1";

# Po nm2 - z alignment variables
pubmsg = "{pubmsgnick $nickalign$2 {pubnick $nickcolor$[.15]0$nicktrunc}}$1";
```

**nm2 Variables**:
- `$nickalign` - Padding dla wyrównania
- `$nickcolor` - Kolor nicka (z nickcolor_expando)
- `$nicktrunc` - Skrócenie długich nicków
- `$[.15]` - Maksymalna szerokość 15 znaków

### Ustawienia w Config
```irssi
settings = {
  "perl/core/scripts" = {
    neat_maxlength = "15";
    neat_dynamic = "yes";
  };
};
```

---

## 3. Integracja nickcolor_expando_simple

### Konfiguracja Kolorów
```irssi
settings = {
  "perl/core/scripts" = {
    nick_colors = "%r %R %g %G %y %b %B %c %C %X42 %X3A %X5E %X4N %X3H %X3C %X32";
    nick_char_sum_hash = "yes";
  };
};
```

### Paleta Nexus Steel
16 kolorów dostosowanych do motywu cyberpunk:
- **Podstawowe**: %r %R %g %G %y %b %B %c %C (9 kolorów)
- **Extended**: %X42 %X3A %X5E %X4N %X3H %X3C %X32 (7 kolorów)

### Integracja z nm2
nickcolor_expando dostarcza `$nickcolor` variable używane przez nm2:
```irssi
# W abstracts motywu
pubnick = "%_%_$*%_%N";

# W formats z nm2 + nickcolor
pubmsg = "{pubmsgnick $nickalign$2 {pubnick $nickcolor$[.15]0$nicktrunc}}$1";
```

---

## 4. Integracja Rotator

### Konfiguracja Animacji
```irssi
settings = {
  "perl/core/scripts" = {
    rotator_chars = "%B%W⚡%k________%k %k_%k%W⚡%k_______%k %k__%k%W⚡%k______%k...";
    rotator_speed = "180";
  };
};
```

### Statusbar Integration
Rotator jest dodany do statusbar info1:
```irssi
statusbar = {
  default = {
    info1 = {
      items = { 
        rotator = { };  # Animowany wskaźnik
      };
    };
  };
};
```

### Cyberpunk Animation
Animacja używa:
- **⚡** - Lightning bolt jako główny element
- **%B→%W→%G** - Gradient blue→white→green
- **180ms** - Płynna animacja
- **Underscores** - Cyberpunk trailing effect

---

## 5. Integracja Statusbar z Motywem

### Custom Items w Motywie
```irssi
statusbar = {
  items = {
    time = "{sb %B∞%n%W❱ %G⚡%n Connected: %G%W$tag%n %W>%B>%C> %WNexus %G<%B<%W<%W}";
    user = "{sb %WU%wser ⇋ %B$cumode%w$N%W : ➜ %WS%wtatus {sbmode (%C+$usermode%n)}}";
    window = "%W ➜ %n{sbmode %WC%whannel (%C+$M%n%W) %B ➜ %WW%window: %G$winref }{sbaway $A}%W  %n";
    prompt = "%w⟨%B$itemname%w⟩: ";
  };
};
```

### UTF-8 Icons w Statusbar
- **∞** - Infinity symbol dla czasu
- **⚡** - Lightning bolt dla połączenia
- **❱** - Right angle bracket
- **⇋** - Exchange symbol dla użytkownika
- **➜** - Arrow dla nawigacji
- **⟨⟩** - Angle brackets dla prompt

### Layout Statusbar
```
┌─────────────────────────────────────────────────────────────┐
│ Topic Bar (top, position=1)                                │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ Main Chat Area                                              │
│                                                             │
├─────────────────────────────────────────────────────────────┤
│ info2: time, act, lag (position=0)                         │
│ info1: user, rotator, window (position=10)                 │
│ prompt: input area (position=100)                          │
└─────────────────────────────────────────────────────────────┘
```

---

## 6. Integracja Spellcheck

### Konfiguracja w Config
```irssi
settings = {
  "perl/core/scripts" = {
    spellcheck_default_language = "pl_PL";
    spellcheck_print_suggestions = "no";
    spellcheck_word_color = "%R";           # Crimson red dla błędów
    spellcheck_word_input_color = "%U%R";   # Underlined red w input
  };
};
```

### Aliasy Spellcheck
```irssi
aliases = {
  SPELL = "spellcheck $*";
  SPELLEN = "spellcheck en $*";
  SPELLPL = "spellcheck pl $*";
};
```

### Integracja z Motywem
Kolory błędów pisowni używają palety Nexus Steel:
- **%R** - Crimson red dla błędów
- **%U** - Underline dla podkreślenia
- Consistent z error formatting w motywie

---

## 7. Integracja Translate

### Konfiguracja API
```irssi
settings = {
  "perl/core/scripts" = {
    translate_api_key = "AIzaSyDUfcmoiserVvtS_jlQ7Tj9s7xM-6Fyti0";
    translate_list_in = "{\"#ditalinux\":{\"target_lang\":\"pl\",\"source_lang\":\"it\"}}";
    translate_list_out = "{\"gosub\":{\"target_lang\":\"en\",\"source_lang\":\"pl\"}}";
    translate_print_original = "yes";
  };
};
```

### Aliasy Translate
```irssi
aliases = {
  TREN = "tr en $*";    # Quick English translation
  TRPL = "tr pl $*";    # Quick Polish translation
  TRDE = "tr de $*";    # Quick German translation
  TRFR = "tr fr $*";    # Quick French translation
};
```

### Multi-Network Support
Translate integruje się z multi-network setup:
- **IRCnet**: Głównie polski (translate_list_out dla "gosub")
- **IRCnet2**: Międzynarodowy (translate_list_in dla "#ditalinux")

---

## 8. Integracja tmux-nicklist

### Konfiguracja
```irssi
settings = {
  "perl/core/scripts" = {
    # Nicklist settings (nie w config, ale w skrypcie)
  };
};
```

### Współpraca z nickcolor
tmux-nicklist używa kolorów z nickcolor_expando:
- Wymaga funkcji `get_nick_color2`
- Używa tej samej palety 16 kolorów
- Sortuje away users na dole

### Tmux Integration
Tworzy osobny panel tmux z:
- **20% szerokości** ekranu
- **Pionowa lista** nicków
- **Mouse support** dla scrollowania
- **Keyboard navigation** (vim-like)

---

## 9. Integracja mh_sbuserinfo

### Statusbar Item
```irssi
statusbar = {
  default = {
    prompt = {
      items = {
        mh_sbuserinfo = { };  # User count info
      };
    };
  };
};
```

### Format Display
```
[Users: <users>(*<opers>:@<ops>:+<voice>:<normal>)/<limit>(<free>)]
```

### Kolory Ostrzeżeń
- **Opless channel**: Zmienia kolor ops count
- **Near limit**: Zmienia kolor limit count
- Używa kolorów z palety Nexus Steel

---

## 10. Przepływ Danych Między Skryptami

### 1. Nick Processing Pipeline
```
Input Nick → nickcolor_expando → $nickcolor variable → nm2 alignment → Theme formatting → Display
```

### 2. Window Activity Pipeline
```
IRC Event → AWL processing → Activity level → Color mapping → Statusbar display
```

### 3. Message Processing Pipeline
```
IRC Message → Spellcheck → Translation (if configured) → nm2 alignment → Theme formatting → Display
```

### 4. Statusbar Update Pipeline
```
System State → rotator animation → mh_sbuserinfo counts → Custom items → Theme styling → Display
```

---

## 11. Konfiguracja Startup

### Startup Command
```irssi
startup = {
  command = "/set theme nexus_steel.theme; /set real_name https://www.ircnet.pl; /window new hidden; /window name Notices; /window level -ALL +NOTICES +INVITES; /window move 2; /window new hidden; /window name IRCnet; /network IRCnet; /window new hidden; /window name IRCnet2; /network IRCnet2; /set real_name https://www.al; /echo === Multi-Network IRC Setup Complete ===";
};
```

### Initialization Sequence
1. **Set theme** → nexus_steel.theme
2. **Create windows** → Notices, IRCnet, IRCnet2
3. **Set networks** → Multi-network setup
4. **Load scripts** → Autorun scripts initialize
5. **Configure statusbar** → Custom items activate
6. **Apply theme** → All formatting applies

---

## 12. Troubleshooting Integration

### Common Issues

#### AWL nie pokazuje kolorów
```irssi
/set awl_viewer OFF
/statusbar window add -after more -alignment right awl_shared
```

#### nm2 nie wyrównuje nicków
```irssi
/set neat_maxlength 15
/set neat_dynamic ON
```

#### nickcolor nie działa
```irssi
/script reload nickcolor_expando_simple
/set nick_colors "%r %R %g %G %y %b %B %c %C"
```

#### Rotator nie animuje
```irssi
/set rotator_speed 180
/statusbar info1 add rotator
```

### Verification Commands
```irssi
/statusbar           # Check statusbar configuration
/script list         # Verify loaded scripts
/set theme           # Confirm theme
/format              # Check format overrides
```

---

## Podsumowanie Integracji

Konfiguracja tworzy spójny ekosystem gdzie:

1. **Motyw Nexus Steel** definiuje wizualną tożsamość
2. **AWL** zarządza listą okien z cyberpunk styling
3. **nm2** wyrównuje nicki z kolorami
4. **nickcolor_expando** dostarcza konsystentne kolorowanie
5. **rotator** animuje statusbar
6. **spellcheck/translate** wspierają komunikację
7. **statusbar** integruje wszystkie elementy
8. **aliasy** ułatwiają zarządzanie

Każdy element jest precyzyjnie dostrojony do pozostałych, tworząc nowoczesny, funkcjonalny i estetyczny interface IRC.

---

*Dokumentacja integracji skryptów z motywem Nexus Steel i konfiguracją irssi*
