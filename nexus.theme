# nexus.theme
# Modern IRC theme for the 21st century nerd/geek
# Inspired by bubble tea, gum, lipgloss with fresh colorscheme
# Features UTF-8 icons, modern styling, and comprehensive AWL/nm2 support

replaces = { ":()=" = "%K$*%n"; };

abstracts = {
  # Core elements with modern styling
  line_start = "%m▸%n ";
  timestamp = "%c⟨ %m$*%c ⟩%n";
  hilight = "%Y$*%n";
  error = "%R✖ $*%n";
  channel = "%c%_$*%_%n";
  channel2 = "%M$C%n";
  nick = "%n$*%n";
  nickhost = "$*";
  server = "%g$*%N";
  comment = "%c⟨%n$*%c⟩%N";
  reason = "%g(%n$*%g)%N";
  mode = "%c⟨%m%_$*%c%_%c⟩%N";

  # Channel nick highlights with modern colors
  channick_hilight = "%m$*%n";
  channick_hilight2 = "%Y$*%n";
  chanhost_hilight = "%g⟨{nickhost $*}⟩%g";
  channick = "%g$*%n";
  chanhost = "%c⟨%g{nickhost $*}%c⟩%n";
  channelhilight = "%c$*%n";
  ban = "$*";

  # Message styling with clean separators
  msgnick = "%g$0%c$1-%c▸%n%|";
  ownmsgnick = "%y$0%c$1-%c▸%n%|";
  ownnick = "%Y%_$*%_%n";
  pubmsgnick = "{msgnick $0 $1-}";
  pubnick = "%_$*%_";
  pubmsgmenick = "{msgnick $0 %_$1-%_%n}";
  menick = "%m$*%n";
  pubmsghinick = "{msgnick $1 $0$2-}";
  msgchannel = "%c▸%n%_$*%_";

  # Private messages with modern brackets
  privmsg = "%c⟨%m$0%c⟩%g⟨{nickhost $1-}⟩%n ";
  ownprivmsg = "%c⟨%y$0%c⟩%g⟨$1-⟩%n ";
  ownprivmsgnick = "%c⟨%y$*%c⟩%n ";
  ownprivnick = "%_$*%_";
  privmsgnick = "%c⟨%m$*%c⟩%n ";

  # Actions with modern asterisk
  action = "%c◦%n";
  ownaction = "{action} %y$0 $1-";
  pvtaction = "%c⟨%gquery%c⟩%g⟨$0⟩%n {action} %c→%n $1-";
  pvtaction_query = "%c◦ $* {action} ";
  pubaction = "{action} %m$* %c◦%n";

  # WHOIS information
  whois = "$[12]0 : $1-";
  whois1 = "$[12]0 : $1-";
  whois2 = "$[12]0 : $1-";
  whois3 = "$[12]0 : $1-";

  # Notices with modern styling
  ownnotice = "%c⟨%gnotice%c⟩%g⟨$1-⟩%n ";
  notice = "%m◆%n %cNotice %m▸%c▸%m▸%n {nick $0}: $1";
  pubnotice_channel = ":$*";
  pvtnotice_host = "%g⟨$*⟩%n";
  servernotice = "{notice $*}";

  # CTCP with clean styling
  ownctcp = "%c⟨%g$0%c⟩%g⟨$1-⟩%n ";
  ctcp = "%c▸%m▸%c▸%n {nick $0} %g$1%n $2 %c$3%n $4 $5 %g$6%n";

  # Wallops
  wallop = " $*: ";
  wallop_nick = " $*";
  wallop_action = " ◦ $* ";

  # Network events with modern colors
  netsplit = "%m$*";
  netjoin = "%c$*";

  # Names list styling
  names_nick = "%m$0%g$[9]1-%n ";
  names_users = "(%c$0%g(%m$1%c))";
  names_channel = "%c$*";

  # DCC transfers
  dcc = "%m$0%n $1 $3 $4 %c$5 $6 $7 $8-%n";
  dccfile = "%_$*%_";
  dccownmsg = "%c⟨%gdcc%c⟩%g⟨$*⟩%n ";
  dccownnick = "%c$*%n";
  dccownaction = "%c⟨%gdcc%c⟩%g⟨$0⟩%n {action} %c→%n $1-";
  dccmsg = "%c⟨%gdcc%c⟩%g⟨$*⟩%n ";
  dccquerynick = "%m$*%n";
  dccaction = "%c⟨%gdcc%c⟩%g⟨$*⟩%n {action}%n %|";

  # Status bar with modern elements
  sb_background = "%0";
  sb_topic_bg = "%c%W";
  sb = "%c▸%n $0-%c ◦%n";
  prompt = "%c⟨%m$0%c⟩%n ";
  sbmode = "$0-";
  sbservertag = ":$0 %n(%gchange with ^X%n)";
  sbmore = " %r◀%R◀%k◀ %nmore %k▶%R▶%r▶ ";
  sblag = "{sb Lagging %r$0-%g seconds!}";
  sb_default_bg = "%4";
  sb_act_sep = "%g/";
  sb_act_text = "%m$*";
  sb_act_msg = "%c$*";
  sb_act_hilight = "%Y$*";
  sb_act_hilight_color = "%m$0$1-%n";
  sb_info1_bg = "%c";
  sb_window_bg = "%m%W";
  sb_window2_bg = "%m%W";
  sb_info2_bg = "%c";

  # User count with modern styling
  sb_usercount = "{sb %cN%getwork:%n %g$tag }{sb %cU%gsers: %m$0 %g$1-";
  sb_uc_normal = "%cN%gormal %Y$*%m]";
  sb_uc_ops = "%m[%cIRC%gOpers %y$mh_opercount %c▸ %cO%gpers %Y$*%c ▸ ";
  sb_uc_voices = "%cV%goice %Y$*%c ▸ ";

  # nm2 and alignment settings
  nickalign = "";
  nickcolor = "%n";
  nicktrunc = "";
  cumode_space = " ";
};

formats = {
  "fe-common/core" = {
    # Modern join/part/kick/quit with UTF-8 symbols instead of text
    join = "%c◈%n %g→%n {channick_hilight %c$0} {chanhost_hilight %g$1}";
    part = "%y◈%n %y←%n {channick_hilight2 %y$0} {chanhost_hilight %g$1} %c{reason $3}";
    kick = "%r◈%n %R✖%n {channick_hilight2 %m$0} by {nick $2} %c{reason $3}";
    quit = "%r◈%n %r⏻%n {channick_hilight2 %y$0}%n {chanhost_hilight %g$1}%n %c{reason $2}";
    notice = "{notice $0 $1}";
    quit_once = "{channel $3} {channick $0} {chanhost $1} {reason $2}";
    servertag = "%m⟨%c$0%m⟩%n ";
    invite = "%c✉%n {nick %m$0} to {channel $1}";
    new_topic = "%c◉%n Topic changed by %m{channick $0} in {channel $1} to: $2";
    topic_unset = "%c◉%n Topic for {channel $1} unset by %m{channick $0}";
    your_nick_changed = "◉ You are now %Y{nick $1} ◦ ";
    nick_changed = "%c◉%n {channick %m$0} %gis now %Y{channick_hilight $1}";
    talking_in = "%c◈%n You are now talking in {channel $0}";
    not_in_channels = "%r◈%n You are not on any channels";
    names = "{names_users %c◦ Users ◦%n} {channel {names_channel $0}} %c▸%n (%m$1) ◦ ";
    names_nick = "{names_nick $0 $1}";
    endofnames = "{channel %g$0}: %cTotal of {hilight %m⟨%c$1%m⟩%c} %gnicks%n {comment %cOps {hilight %m⟨%c$2%m⟩%c}, Voice %m⟨{hilight %c$4%m}⟩%c, Normal %m⟨{hilight %c$5%m}⟩%c}";

    # Message formats with nm2 support
    pubmsg = "{pubmsgnick $nickalign$2 {pubnick $nickcolor$[.14]0$nicktrunc}}$1";
    own_msg = "{ownmsgnick $nickalign$2 {ownnick $nickcolor$0$nicktrunc}}$1";
    own_msg_channel = "{ownmsgnick $nickalign$3 {ownnick $nickcolor$0$nicktrunc}{msgchannel $1}}$2";
    pubmsg_me = "{pubmsgmenick $nickalign$2 {menick $[.14]0$nicktrunc}}$1";
    pubmsg_me_channel = "{pubmsgmenick $nickalign$3 {menick $[.14]0$nicktrunc}{msgchannel $1}}$2";
    pubmsg_hilight = "{pubmsghinick $0 $nickalign$3 $[.14]1$nicktrunc}$2";
    pubmsg_hilight_channel = "{pubmsghinick $0 $nickalign$4 $[.14]1$nicktrunc{msgchannel $2}}$3";
    pubmsg_channel = "{pubmsgnick $nickalign$3 {pubnick $nickcolor$[.14]0$nicktrunc}{msgchannel $1}}$2";
    own_msg_private_query = "{ownprivmsgnick $nickalign{ownprivnick $nickcolor$2$nicktrunc}}$1";
    msg_private_query = "{privmsgnick $nickalign$nickcolor$0$nicktrunc}$2";
  };
  "fe-common/irc" = {
    inviting = "%c✉%n Inviting {nick %m$0} to {channel $1}";
    topic_info = "%c◉%n Topic set by %m{channick $0} {comment $1}";
    server_chanmode_change = "{netsplit %cServerMode%n}/{channelhilight $0} {mode $1} by {nick $2}";
    whois = "{nick $0} ({nickhost $1%m@%n$2}) {comment $4}%:{whois %_i%_rcname $3}";
    whowas = "           %cWHOWAS%g       %n%:%m*%n {nick $0} ({nickhost $1%m@%n$2})%:{whois %_i%_rcname $3}";
    whois_idle = "{whois3 %_i%_dle %|$1 days $2 hours $3 mins $4 secs}";
    whois_idle_signon = "{whois3 %_i%_dle %|$1 days $2 hours $3 mins $4 secs (signon: $5)}";
    whois_server = "{whois2 %_s%_erver %|$1 ($2)}";
    whois_oper = "{whois3 %_o%_perator {hilight $1}}";
    whois_registered = "{whois %_a%_uth has registered this nick}";
    whois_help = "{whois %_d%_uty is available for help}";
    whois_modes = " {whois %_m%_odes $1}";
    whois_realhost = "{whois %_h%_ostname $1-}";
    whois_usermode = "{whois %_u%_sermode $1}";
    whois_channels = "{whois %_c%_hannels %|$1}";
    whois_away = "{whois %_a%_way %c%|$1}";
    whois_special = "{whois  %|{hilight $1}}";
    end_of_whois = "%cEnd of WHOIS ◦ ";
    end_of_whowas = "%gEnd of WHOWAS ◦ ";
    whois_not_found = "%gThere is no such Nick:%r.%g {nick %c$0}";
    who = "{channelhilight %c$[!10]0%n} %|{nick $[!9]1} %c$[!3]2%n $[!2]3 $4%m@%n$5 :: $6";
    ctcp_requested = "requested $2 from {nick $3} {ctcp {hilight $0} {comment $1}}";
    own_action = "$nickalign{ownaction $0$nicktrunc}$1";
    action_private = "$nickalign{pvtaction $0$nicktrunc}$2";
    action_private_query = "$nickalign{pvtaction_query $0$nicktrunc}$2";
    action_public = "$nickalign{pubaction $0$nicktrunc}$1";
  };
  "fe-common/irc/dcc" = {
    own_dcc = "{dccownmsg {dccownnick $1}}$2";
    own_dcc_action = "{dccownaction_target $0 $1}$2";
    own_dcc_action_query = "{dccownaction $0}$2";
    own_dcc_ctcp = "{ownctcp ctcp $0}$1 $2";
    dcc_msg = "{dccmsg $0}$1";
    action_dcc = "{dccaction $0}$1";
    action_dcc_query = "{dccaction $0}$1";
    own_dcc_query = "{dccownmsg {dccownnick $0}}$2";
    dcc_msg_query = "{dccmsg $0}$1";
    dcc_ctcp = "{dcc %c▸▸▸ DCC CTCP {hilight $1} received from {hilight $0}: $2}";
  };
  # nm2 (nick alignment) configuration with Nexus Steel styling
  nm2 = {
    neat_pad_char = " ";
    neat_left_actions = "0";
    neat_right_actions = "1";
    neat_left_messages = "0";
    neat_right_messages = "1";
    neat_maxlength = "15";
    neat_colors = "1";
    neat_shrink_uniq = "1";
  };

  # Advanced window list (AWL) configuration with Nexus Steel styling
  "Irssi::Script::adv_windowlist" = {
    # Active window with electric blue accent and power symbol
    awl_display_key_active = "%m◉ $N %c${cumode_space}%y$H$C$S";

    # Inactive windows with subtle steel gray styling  
    awl_display_key = "  %g$N %c${cumode_space}%g$H$C$S";

    # Background styling consistent with statusbar
    awl_viewer_item_bg = "%N";

    # Clean header without decoration
    awl_display_header = "";

    # Windows without hotkeys using steel gray palette
    awl_display_nokey = "  %g$N %c${cumode_space}%g$H$C$S";

    # Visible windows with cyan accent and visibility indicator
    awl_display_nokey_visible = "%c◦ %g$N %c${cumode_space}%g$H%N$C$S";
    awl_display_key_visible = "%c◦ %g$N %c${cumode_space}%g$H%N$C$S";

    # Active window without hotkey using power symbol
    awl_display_nokey_active = "%m◉ $N %c${cumode_space}%y$H$C$S";

    # Activity level colors following Nexus Steel palette
    awl_data_level_none = "%7";
    # silver-gray for no activity
    awl_data_level_low = "%c";
    # sky-blue for low activity  
    awl_data_level_medium = "%3";
    # amber-yellow for medium activity
    awl_data_level_high = "%1";
    # crimson-red for high activity/highlights

    # Additional styling options for enhanced visual consistency
    awl_separator = "%8▸%f";
    awl_abbrev_chars = "→";
    awl_hide_empty = "0";
    awl_maxlines = "0";
    awl_sort = "refnum";
  };

  # nm2 script configuration with enhanced Nexus Steel integration
  "Irssi::Script::nm2" = {
    neat_pad_char = " ";
    neat_left_actions = "0";
    neat_right_actions = "1";
    neat_left_messages = "0";
    neat_right_messages = "1";
    neat_maxlength = "15";
    neat_colors = "1";
    neat_shrink_uniq = "1";
    neat_colorize = "1";
    neat_allow_shrinking = "1";
    neat_melength = "15";
  };
};
