use Irssi;
use strict;
use warnings;
use JSON;

# Stałe
my $password = 'G181!70!f4j4!';
my $hubhost = '************';
my $hubport = '4446';
# Usta<PERSON><PERSON>e domyślnego aclnick
Irssi::settings_add_str('misc', 'aclnick', 'yooz');
my $aclnick = Irssi::settings_get_str('aclnick');

# Rejestracja komend
Irssi::command_bind('aop', 'cmd_aop');
Irssi::command_bind('shit', 'cmd_shit');
Irssi::command_bind('acl', 'cmd_acl');

sub mask_to_regex {
    my $mask = shift;
    my ($nick, $ident_host) = split /!/, $mask;
    return $ident_host;  # <PERSON><PERSON>rac<PERSON>y tylko c<PERSON> ident@host
}

sub send_to_hub_aopshit {
    my ($command) = @_;
    return `echo '$password $command' | nc $hubhost $hubport`;
}

sub is_aop_match {
    my ($channel, $address) = @_;
    my ($ident, $host) = split /@/, $address;
    my $ident_host = $ident . "@" . $host;

    my $response = send_to_hub_aopshit("acl list $aclnick aops");
    my $data = decode_json($response);


    if (exists $data->{$channel}) {
        foreach my $nick (keys %{$data->{$channel}}) {
            foreach my $stored_hostmask (@{$data->{$channel}{$nick}}) {
                if ($ident_host =~ mask_to_regex($stored_hostmask)) {
                    Irssi::print("%GZnaleziono dopasowanie AOP dla $nick!");
                    return 1;
                }
            }
        }
    }
    return 0;
}

sub get_shit_match {
    my ($channel, $address) = @_;
    my ($ident, $host) = split /@/, $address;
    my $ident_host = $ident . "@" . $host;

    my $response = send_to_hub_aopshit("acl list $aclnick shits");
    my $data = decode_json($response);

    Irssi::print("%GSprawdzanie dopasowania SHIT dla kanału $channel i hosta $ident_host...%n");

    if (exists $data->{$channel}) {
        foreach my $nick (keys %{$data->{$channel}}) {
            my $stored_recognition_host = $data->{$channel}{$nick}{'recognition_host'};
            if ($ident_host =~ mask_to_regex($stored_recognition_host)) {
                Irssi::print("%GZnaleziono dopasowanie SHIT dla $nick!");
                return ($data->{$channel}{$nick}{'banmask'}, $data->{$channel}{$nick}{'kickreason'});
            }
        }
    }
    return (0, 0);
}

sub on_user_join {
    my ($server, $channel, $nick, $address) = @_;
    my ($ident, $host) = split /@/, $address;

    if (is_aop_match($channel, $address)) {
        $server->command("MODE $channel +o $nick");
    }

    my ($banmask, $kickreason) = get_shit_match($channel, $address);
    if ($banmask) {
        $server->command("MODE $channel +b $banmask");
        $server->command("KICK $channel $nick $kickreason");
    }
}

sub process_aop_response {
    my ($response) = @_;
    my $data = decode_json($response);
    Irssi::print("%GAOP List:");
    for my $channel (keys %$data) {
        Irssi::print("%G├─ Channel: $channel");
        for my $nick (keys %{$data->{$channel}}) {
            Irssi::print("%G│  ├─ Nick: $nick");
            for my $hostmask (@{$data->{$channel}{$nick}}) {
                Irssi::print("%G│  │  └─ Hostmask: $hostmask");
            }
        }
    }
}
sub process_shits_response {
    my ($response) = @_;
    my $data = decode_json($response);
    Irssi::print("%GShits List:");
    for my $channel (keys %$data) {
        Irssi::print("%G├─ Channel: $channel");
        for my $nick (keys %{$data->{$channel}}) {
            Irssi::print("%G│  ├─ Nick: $nick");
            Irssi::print("%G│  │  ├─ Recognition Host: " . $data->{$channel}{$nick}{'recognition_host'});
            Irssi::print("%G│  │  ├─ Banmask: " . $data->{$channel}{$nick}{'banmask'});
            Irssi::print("%G│  │  └─ Kick Reason: " . $data->{$channel}{$nick}{'kickreason'});
        }
    }
}

sub send_to_hub {
    my ($command) = @_;
    my $response = `echo '$password $command' | nc $hubhost $hubport`;
    chomp $response;
    if ($command =~ /acl list .* aops/) {
        process_aop_response($response);
    } elsif ($command =~ /acl list .* shits/) {
        process_shits_response($response);
    } else {
        Irssi::print($response);
    }
}

sub cmd_aop {
    my ($data, $server, $witem) = @_;
    my $aclnick = Irssi::settings_get_str('aclnick');
    my ($action, @params) = split(' ', $data);

    if (not defined $action) {
        Irssi::print("%GNiepoprawne użycie komendy /aop. Użyj /acl, aby zobaczyć dostępne komendy.%n");
        return;
    }

    if ($action eq 'add') {
        send_to_hub("acl add $aclnick aops @params");
    } elsif ($action eq 'del') {
        send_to_hub("acl del $aclnick aops @params");
    } elsif ($action eq 'list') {
        send_to_hub("acl list $aclnick aops");
    } else {
        Irssi::print("%GNieznana komenda aop. Użyj /acl, aby zobaczyć dostępne komendy.%n");
    }
}

sub cmd_shit {
    my ($data, $server, $witem) = @_;
    my $aclnick = Irssi::settings_get_str('aclnick');
    my ($action, @params) = split(' ', $data);

    if (not defined $action) {
        Irssi::print("%GNiepoprawne użycie komendy /shit. Użyj /acl, aby zobaczyć dostępne komendy.%n");
        return;
    }

    if ($action eq 'add') {
        send_to_hub("acl add $aclnick shits @params");
    } elsif ($action eq 'del') {
        send_to_hub("acl del $aclnick shits @params");
    } elsif ($action eq 'list') {
        send_to_hub("acl list $aclnick shits");
    } else {
        Irssi::print("%GNieznana komenda shit. Użyj /acl, aby zobaczyć dostępne komendy.%n");
    }
}

sub cmd_acl {
    Irssi::print("%G/aop add [channel] [nick] [hostmask] %M-%W Dodaje aop dla nicka i hostmasku");
    Irssi::print("%G/aop del [channel] [nick] [hostmask] %M-%W Usuwa aop dla nicka i hostmasku");
    Irssi::print("%G/aop list %M-%W Wyświetla listę aop");
    Irssi::print("%G/shit add [channel] [nick] [recognition_host] [banmask] [kick_reason] %M-%W Dodaje shit dla nicka");
    Irssi::print("%G/shit del [channel] [nick] [recognition_host] %M-%W Usuwa shit dla nicka");
    Irssi::print("%G/shit list %M-%W Wyświetla listę shit");
}

Irssi::signal_add('message join', 'on_user_join');
Irssi::print("%GSkrypt AccessList (plik acl.pl) %M-%W Aop/shitlist dla irssi został załadowany!");
