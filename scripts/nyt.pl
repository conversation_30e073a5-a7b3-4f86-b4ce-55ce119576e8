#!/usr/bin/perl
use strict;
use warnings;
use Irssi;
use LWP::UserAgent;
use HTTP::Cookies;
use HTML::TreeBuilder;
use JSON;
 
our $VERSION = "0.5";
our %IRSSI = (
    authors     => 'nosfar',
    contact     => '<EMAIL>',
    name        => 'YT_Streamer',
    description => 'Prints YouTube live link and title if a live stream is active',
    license     => 'Public Domain',
);
 
# Lista streamerow
my @yt_channels = (
    {
        name => 'mxlooo',
        id  => "UChaX3iJcrSFeigHltCaeR-w",
    chan => "#contempt",
    },
    {
        name => 'K6A6T6',
        id  => "UCeu-Yo5DFxbnxzAichfZS-Q",
    chan => "#contempt",
    },
    {
        name => 'Karool',
        id  => "UCLjzFhnD6sBGNVIvuc9XgQA",
    chan => "#contempt",
    },
#    {
#   name => 'Phixate',
#   id => "UCVT-8350tCpSc7_X4dBhneg",
#   chan => "",
#    },
#    {
#   name => 'Masurai',
#   id => "UCmfHWeN3syhe1D-ztrdLfNg",
#   chan => "#contempt",
#    },
    {
    name => 'nosfar',
    id => "UCLfHe0FEMkY8C6DGN7m7Ahg",
    chan => "#miziakowo",
    },
);
 
my %displayed;
#my $target = "#contempt";
 
my $api_key = '';
 
 
 
my $channelTitle = '';
my $title = '';
my $actualStartTime = '';
my $actualEndTime = '';
my $viewCount  = '';
my $video_id = '';
my $stream_start = '';
my $stream_end = '';
my $duration = '';
my $year = '';
my $month = '';
my $day = '';
my $hour = '';
my $minute = '';
my $second = '';
my $hours = '0';
my $minutes = '0'; 
my $seconds = '0';
my ($txt_h, $txt_m, $txt_s) = ('', '', '');
 
sub check_youtube_live {
 
    foreach my $channel (@yt_channels) {
        my $channel_id  = $channel->{id};
        my $channel_name = $channel->{name};
    my $target = $channel->{chan};
 
        my $base_url = "https://www.youtube.com/channel/$channel_id";
        my $channel_url = "$base_url/live";
        my $ua = LWP::UserAgent->new;
        # Use a cookie jar and set the consent cookie
        $ua->cookie_jar(HTTP::Cookies->new);
        $ua->default_header('Cookie' => 'CONSENT=YES+42');
 
        my $response = $ua->get($channel_url);
        if ($response->is_success) {
        my $content = $response->decoded_content;
        my $tree = HTML::TreeBuilder->new_from_content($content);
 
        $tree = $tree->as_HTML;
        my $canonical = $tree->look_down(_tag => 'link', rel => 'canonical');
 
        my $live_url = $canonical->attr('href');
        if ($live_url eq $base_url) {
 
 
 
        if ($displayed{$channel_name} ne "") {
 
            #check endstream
            my $api_url_e = "https://www.googleapis.com/youtube/v3/videos?id=$displayed{$channel_name}&key=$api_key&part=snippet,contentDetails,statistics,status,liveStreamingDetails";
            my $ua_e = LWP::UserAgent->new;
            my $response_e = $ua_e->get($api_url_e);
 
            if ($response_e->is_success) {
                my $content_e = $response_e->decoded_content;
                my $data_e = eval { decode_json($content_e) };
                if ($@) {
                    Irssi::print("Error decoding JSON: $@");
                    return;
                }
                if ($data_e->{items} && @{$data_e->{items}}) {
                    my $item_e = $data_e->{items}->[0];
 
                    $channelTitle   = $item_e->{snippet}->{channelTitle} // 'N/A';
                    $duration          = $item_e->{contentDetails}->{duration} // 'N/A';
#                   $actualStartTime = $item->{liveStreamingDetails}->{actualStartTime} // 'N/A';
                    $actualEndTime   = $item_e->{liveStreamingDetails}->{actualEndTime} // 'N/A';
                    $viewCount      = $item_e->{statistics}->{viewCount} // 'N/A';
                if ($actualEndTime =~ /(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2})Z/) {
                    my ($year, $month, $day, $hour, $minute, $second) = ($1, $2, $3, $4, $5, $6);
                $stream_end = "$year\-$month\-$day $hour\:$minute\:$second";
                }
                $duration =~ s/^..//;
#               if ($duration =~ /(\d+)H(\d+)M(\d+)S/) {
#                   ($hours, $minutes, $seconds) = ($1, $2, $3);
                if ($duration =~ /(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/) {
                $hours   = $1 if defined $1;
                $minutes = $2 if defined $2;
                $seconds = $3 if defined $3;
                if ($hours ne '0') { $txt_h = $hours ."h ";}
                if ($minutes ne '0') { $txt_m = $minutes ."m ";}
                if ($seconds ne '0') { $txt_s = $seconds ."s ";}
                Irssi::print("Hours: $hours\n");
                Irssi::print("Minutes: $minutes\n");
                Irssi::print("Seconds: $seconds\n");
                }
            } else {
                    Irssi::print("No video found with ID $video_id");
                }
            } else {
                Irssi::print("HTTP GET error: " . $response_e->status_line);
            }
            my $end = "\x{03}12$channelTitle \x{03}03stream has ended \x{03}05\@ \x{03}00$stream_end \x{03}03with dutarion \x{03}00". $txt_h . $txt_m . $txt_s ."\x{03}03and \x{03}00$viewCount \x{03}03views!";
            Irssi::command("msg -IRCnet2 -channel $target $end");
        }
        $displayed{$channel_name} = "";
        } else {
        if ($displayed{$channel_name} eq "") {
 
            #check startstream
            if ($live_url =~ /v=([^&]+)/) {
                $video_id = $1;
            }
            my $api_url = "https://www.googleapis.com/youtube/v3/videos?id=$video_id&key=$api_key&part=snippet,contentDetails,statistics,status,liveStreamingDetails";
            my $ua_s = LWP::UserAgent->new;
            my $response_s = $ua_s->get($api_url);
 
            if ($response_s->is_success) {
                my $content_s = $response_s->decoded_content;
                my $data_s = eval { decode_json($content_s) };
                if ($@) {
                    Irssi::print("Error decoding JSON: $@");
                    return;
                }
                if ($data_s->{items} && @{$data_s->{items}}) {
                    my $item_s = $data_s->{items}->[0];
 
                    $channelTitle   = $item_s->{snippet}->{channelTitle} // 'N/A';
                    $title          = $item_s->{snippet}->{title} // 'N/A';
                    $actualStartTime = $item_s->{liveStreamingDetails}->{actualStartTime} // 'N/A';
#                   my $actualEndTime   = $item->{liveStreamingDetails}->{actualEndTime} // 'N/A';
#                   my $viewCount      = $item->{statistics}->{viewCount} // 'N/A';
                if ($actualStartTime =~ /(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2})Z/) {
                    ($year, $month, $day, $hour, $minute, $second) = ($1, $2, $3, $4, $5, $6);
                $stream_start = "$year\-$month\-$day $hour\:$minute\:$second";
                }
            } else {
                    Irssi::print("No video found with ID $video_id");
                }
            } else {
                Irssi::print("HTTP GET error: " . $response_s->status_line);
            }
            my $message = "\x{03}12$channelTitle \x{0f}\x{03}03\|\x{03}08 $title \x{0f}\x{03}03is live: \x{0f}\x{03}00$stream_start \x{0f}\x{03}05\@\x{03}03address: \x{03}00$live_url";
            Irssi::command("msg -IRCnet2 -channel $target $message");
            $displayed{$channel_name} = $video_id;
        } else {
            #check startstream
            if ($live_url =~ /v=([^&]+)/) {
                $video_id = $1;
            }
            if ($displayed{$channel_name} ne $video_id) {
            $displayed{$channel_name} = $video_id;
            }
        }
        }
        $tree->delete;
    } else {
            Irssi::print("Error fetching YouTube page: " . $response->status_line);
    }
    }
}
 
# Timer callback: check every 2 minutes (2 * 60 * 1000 ms)
Irssi::timeout_add(1 * 36 * 1000, \&check_youtube_live, undef);
 
Irssi::print("YouTube live bot loaded.");