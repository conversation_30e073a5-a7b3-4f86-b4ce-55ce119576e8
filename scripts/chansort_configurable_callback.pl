use strict;
use warnings;

sub {
    # Funk<PERSON><PERSON> pomocnicza do określenia priorytetu typu okna WEWNĄTRZ sieci
    my $get_window_priority = sub {
        my $win = shift;
        
        # Okna telnet mają specjalny priorytet
        return -1 if $win->{name} eq 'telnet';
        
        # W ramach sieci: SERVER → CHANNEL → QUERY
        return 1 if $win->{type} eq 'SERVER';
        return 2 if $win->{type} eq 'CHANNEL';
        return 3 if $win->{type} eq 'QUERY';
        return 4; # Inne na koń<PERSON>
    };
    
    my $a_priority = $get_window_priority->($a);
    my $b_priority = $get_window_priority->($b);
    
    # Najpierw telnet na górze
    return $a_priority <=> $b_priority if ($a_priority == -1 || $b_priority == -1);
    
    # Potem grupujemy według chatnet (alfabetycznie)
    my $chatnet_cmp = ($a->{chatnet} || '') cmp ($b->{chatnet} || '');
    return $chatnet_cmp if $chatnet_cmp != 0;
    
    # W ramach tej samej sieci sortujemy według typu okna
    my $type_cmp = $a_priority <=> $b_priority;
    return $type_cmp if $type_cmp != 0;
    
    # W ramach tego samego typu i sieci sortujemy alfabetycznie
    return $a->{name} cmp $b->{name};
};
