#!/usr/bin/perl

use strict;
use warnings;
use Irssi;

# Script info
our $VERSION = '1.0';
our %IRSSI = (
    authors     => 'yooz',
    contact     => '<EMAIL>',
    name        => 'alias_test',
    description => 'Test aliases and provide debugging',
    license     => 'GPLv2',
    url         => 'https://irssi.org',
);

# Test aliases function
sub cmd_alias_test {
    my ($data, $server, $witem) = @_;
    
    Irssi::print("=== Alias Test Script ===");
    Irssi::print("Testing basic aliases...");
    
    # Test some basic aliases
    Irssi::command("echo Testing alias J (join)");
    Irssi::command("echo Testing alias WJOIN (join -window)");
    Irssi::command("echo Testing alias Q (query)");
    
    # Show server info if available
    if ($server) {
        Irssi::print("Current server tag: " . ($server->{tag} || "unknown"));
    }
    
    Irssi::print("=== Alias Test Complete ===");
}

# Debug aliases function
sub cmd_alias_debug {
    my ($data, $server, $witem) = @_;
    
    Irssi::print("=== Alias Debug Info ===");
    
    # Test if aliases are working
    Irssi::print("Testing if aliases are loaded...");
    
    # Test specific aliases
    Irssi::print("Testing network switching aliases:");
    Irssi::command("echo NET1 should switch to IRCnet");
    Irssi::command("echo NET2 should switch to IRCnet2");
    
    # Test basic aliases
    Irssi::print("Testing basic aliases:");
    Irssi::command("echo J should be alias for join");
    Irssi::command("echo W1 should switch to window 1");
    Irssi::command("echo SAVE should save configuration");
    
    # Show current network info safely
    my $server_info = get_server_info($server);
    Irssi::print("Current server: " . $server_info->{tag});
    Irssi::print("Server address: " . $server_info->{real_address});
    Irssi::print("Status: " . $server_info->{status});
    
    Irssi::print("=== Debug Complete ===");
    Irssi::print("Note: 'settings_get(aliases) : not found' is normal - aliases are in config file");
}

# Add test aliases
sub add_test_aliases {
    # Add some test aliases that should work
    Irssi::command("alias TEST1 echo Test alias 1 works");
    Irssi::command("alias TEST2 echo Test alias 2 works");
    Irssi::command("alias TEST3 echo Test alias 3 works");
    
    Irssi::print("Added test aliases: TEST1, TEST2, TEST3");
}

# Remove test aliases
sub remove_test_aliases {
    Irssi::command("unalias TEST1");
    Irssi::command("unalias TEST2");
    Irssi::command("unalias TEST3");
    
    Irssi::print("Removed test aliases");
}

# Test if specific aliases work
sub cmd_test_alias {
    my ($data, $server, $witem) = @_;
    
    if (!$data) {
        Irssi::print("Usage: /test_alias <alias_name>");
        return;
    }
    
    Irssi::print("Testing alias: $data");
    Irssi::print("Try running: /$data");
    Irssi::print("If it works, the alias is loaded correctly");
}

# Show all available aliases
sub cmd_show_aliases {
    my ($data, $server, $witem) = @_;
    
    Irssi::print("=== Available Aliases ===");
    Irssi::print("Network aliases:");
    Irssi::print("  NET1, NET2, NETLIST, NETSTATUS");
    Irssi::print("Window aliases:");
    Irssi::print("  W1, W2, W3, W4, W5, WINDOWLIST");
    Irssi::print("Basic aliases:");
    Irssi::print("  J, Q, M, T, C, SAVE, RELOAD");
    Irssi::print("IRCop aliases:");
    Irssi::print("  KLINE, UNKLINE, GLINE, UNGLINE, OPHELP");
    Irssi::print("Info aliases:");
    Irssi::print("  CONN_INFO, NETWORK_INFO, SERVER_INFO");
    Irssi::print("Use /alias list to see all aliases");
    
    # Show current server info safely
    if ($server && $server->{tag}) {
        Irssi::print("Current server: " . $server->{tag});
    }
}

# Show connection status
sub cmd_connection_status {
    my ($data, $server, $witem) = @_;
    
    Irssi::print("=== Connection Status ===");
    
    # Get current server info
    my $server_info = get_server_info($server);
    Irssi::print("Current server tag: " . $server_info->{tag});
    Irssi::print("Current server address: " . $server_info->{real_address});
    Irssi::print("Current server status: " . $server_info->{status});
    
    # Show all servers with better status checking
    Irssi::print("=== All Servers ===");
    foreach my $srv (Irssi::servers()) {
        my $info = get_server_info($srv);
        Irssi::print("  " . $info->{tag} . " (" . $info->{configured_address} . ":" . $info->{configured_port} . ") - " . $info->{status});
    }
    
    # Show detailed connection info
    Irssi::print("=== Detailed Connection Info ===");
    if ($server) {
        Irssi::print("Server object exists");
        Irssi::print("Connected flag: " . ($server->{connected} ? "true" : "false"));
        
        if ($server->{connrec}) {
            Irssi::print("Connection record exists");
            Irssi::print("Configured address: " . ($server->{connrec}->{address} || "unknown"));
            Irssi::print("Configured port: " . ($server->{connrec}->{port} || "unknown"));
        } else {
            Irssi::print("No connection record");
        }
        
        # Show connection status
        if ($server->{connected}) {
            Irssi::print("Server is actually connected");
        } else {
            Irssi::print("Server is not connected (use /connect " . $server->{tag} . " to connect)");
        }
    } else {
        Irssi::print("No server object");
    }
    
    # Show connection help
    Irssi::print("=== Connection Help ===");
    Irssi::print("To connect to a server, use: /connect <server_tag>");
    Irssi::print("Example: /connect IRCnet");
    Irssi::print("Example: /connect IRCnet2");
    Irssi::print("Or use aliases: /CONN1, /CONN2, /CONNECT_IRCNET, /CONNECT_IRCNET2");
}

# Test connection
sub cmd_test_connection {
    my ($data, $server, $witem) = @_;
    
    Irssi::print("=== Connection Test ===");
    
    if (!$data) {
        Irssi::print("Usage: /test_connection <server_tag>");
        Irssi::print("Available servers:");
        foreach my $srv (Irssi::servers()) {
            my $info = get_server_info($srv);
            Irssi::print("  " . $info->{tag} . " (" . $info->{configured_address} . ":" . $info->{configured_port} . ")");
        }
        return;
    }
    
    Irssi::print("Testing connection to: $data");
    Irssi::print("Attempting to connect...");
    Irssi::command("connect $data");
    
    # Wait a moment and check status
    Irssi::timeout_add_once(3000, sub {
        my $current_server = Irssi::server_find_tag($data);
        if ($current_server && $current_server->{connected}) {
            Irssi::print("✓ Successfully connected to $data");
        } else {
            Irssi::print("✗ Failed to connect to $data");
            Irssi::print("Check if the server is reachable and try again");
        }
    }, undef);
}

# Get server info safely
sub get_server_info {
    my ($server) = @_;
    
    my $info = {};
    
    if ($server) {
        $info->{tag} = $server->{tag} || "unknown";
        
        # Check if server is actually connected
        $info->{connected} = $server->{connected} ? 1 : 0;
        
        # Get configured connection info
        if ($server->{connrec}) {
            $info->{configured_address} = $server->{connrec}->{address} || "unknown";
            $info->{configured_port} = $server->{connrec}->{port} || "unknown";
        } else {
            $info->{configured_address} = "unknown";
            $info->{configured_port} = "unknown";
        }
        
        # Determine real status and address
        if ($server->{connected}) {
            $info->{status} = "connected";
            # If connected, use configured address as real address
            $info->{real_address} = $info->{configured_address};
        } else {
            $info->{status} = "disconnected";
            $info->{real_address} = "not connected";
        }
    } else {
        $info->{tag} = "no server";
        $info->{connected} = 0;
        $info->{status} = "no server";
        $info->{configured_address} = "not connected";
        $info->{configured_port} = "not connected";
        $info->{real_address} = "not connected";
    }
    
    return $info;
}

# Register commands
Irssi::command_bind('alias_test', 'cmd_alias_test');
Irssi::command_bind('alias_debug', 'cmd_alias_debug');
Irssi::command_bind('alias_add_test', 'add_test_aliases');
Irssi::command_bind('alias_remove_test', 'remove_test_aliases');
Irssi::command_bind('test_alias', 'cmd_test_alias');
Irssi::command_bind('show_aliases', 'cmd_show_aliases');
Irssi::command_bind('connection_status', 'cmd_connection_status');
Irssi::command_bind('test_connection', 'cmd_test_connection');

# Print help
Irssi::print("Alias Test Script loaded!");
Irssi::print("Commands available:");
Irssi::print("  /alias_test - Test basic aliases");
Irssi::print("  /alias_debug - Debug alias issues");
Irssi::print("  /alias_add_test - Add test aliases");
Irssi::print("  /alias_remove_test - Remove test aliases");
Irssi::print("  /test_alias <name> - Test specific alias");
Irssi::print("  /show_aliases - Show available aliases");
Irssi::print("  /connection_status - Show connection status");
Irssi::print("  /test_connection <tag> - Test server connection");