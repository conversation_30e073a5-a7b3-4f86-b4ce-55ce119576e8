use strict;
use warnings;
use Irssi;
use File::Glob ':glob';

use vars qw($VERSION %IRSSI);

$VERSION = "1.0";
%IRSSI = (
    authors     => 'yooz',
    contact     => '<EMAIL>',
    name        => 'reloadscripts',
    description => 'Reload all scripts on !reload command from trusted user',
    license     => 'Public Domain',
);

my @trusted_masks = ('*!<EMAIL>', '*!<EMAIL>');

Irssi::signal_add('message public', 'sig_message');
Irssi::signal_add('message private', 'sig_message');

sub mask_match {
    my ($mask, $trusted) = @_;
    $trusted =~ s/\*/.*/g;
    $trusted =~ s/\?/./g;
    return $mask =~ /^$trusted$/i;
}

sub sig_message {
    my ($server, $msg, $nick, $address, $target) = @_;

    my $mask = "$nick!$address";
    return unless mask_match($mask, $trusted_masks[0]);

    my $reply_target = $target =~ /^#/ ? $target : $nick;

    if ($msg =~ /^!reload\b/i) {
        my $count = reload_all_scripts();
        $server->command("MSG $reply_target Przeladowano $count skrypt(ów).");
    }
}

sub reload_all_scripts {
    my $scriptdir = Irssi::get_irssi_dir() . "/scripts";
    my @scripts = bsd_glob("$scriptdir/*.pl");
    my @autorun = bsd_glob("$scriptdir/autorun/*.pl");
    my %all = map { $_ => 1 } (@scripts, @autorun);

    my $count = 0;
    foreach my $script (keys %all) {
        if (-f $script) {
            Irssi::command("script load $script");
            $count++;
        }
    }
    return $count;
}

Irssi::print("Script reloadscripts.pl loaded. Only $trusted_masks[0] can use !reload."); 