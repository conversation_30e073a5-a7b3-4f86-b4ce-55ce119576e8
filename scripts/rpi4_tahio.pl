use strict;
use warnings;
use Irssi;

use vars qw($VERSION %IRSSI);

$VERSION = "1.2";
%IRSSI = (
    authors     => 'yooz',
    contact     => '<EMAIL>',
    name        => 'rpi4_tahio',
    description => 'Zdalne komendy aby weryfikowac stan RPi4',
    license     => 'Public Domain',
);

my @trusted_masks = ('*!<EMAIL>', '*!<EMAIL>');

Irssi::signal_add('message public', 'sig_message');
Irssi::signal_add('message private', 'sig_message');

my @help_lines = (
    '!uptime      - pokazuje uptime serwera',
    '!uptimefull  - szczegółowy uptime i od kiedy system działa',
    '!df          - pokazuje wolne miejsce na dyskach (rozszerzone)',
    '!dfdetail    - szczegółowe informacje o dyskach z iostat',
    '!free        - szczegółowe użycie pamięci RAM (alias !ram)',
    '!ram         - szczegółowe użycie pamięci RAM + statystyki vmstat',
    '!uname       - pokazuje informacje o systemie',
    '!temp        - pokazuje temperaturę CPU',
    '!whoami      - pokazuje użytkownika, pod którym działa Irssi',
    '!aptupdate   - sprawdza dostępne aktualizacje APT',
    '!services    - lista aktywnych usług systemowych',
    '!network     - informacje o sieci i połączeniach',
    '!load        - średnie obciążenie systemu',
    '!processes   - top procesy według CPU i RAM',
    '!diskio      - statystyki I/O dysków',
    '!logs        - ostatnie wpisy z logów systemowych',
    '!help        - pokazuje tę pomoc',
);

sub mask_match {
    my ($mask, @trusted) = @_;
    foreach my $trusted (@trusted) {
        my $pattern = $trusted;
        $pattern =~ s/\*/.*/g;
        $pattern =~ s/\?/./g;
        return 1 if $mask =~ /^$pattern/i;
    }
    return 0;
}

sub sig_message {
    my ($server, $msg, $nick, $address, $target) = @_;

    # Akceptuj na wszystkich kanałach i sieciach
    # return unless (defined $server->{chatnet} && $server->{chatnet} eq 'ircnet2');
    # return unless ($target eq '#contempt');

    my $mask = "$nick!$address";
    return unless mask_match($mask, @trusted_masks);

    my $reply_target = $target =~ /^#/ ? $target : $nick;

    if ($msg =~ /^!uptime\b/i) {
        reply($server, $reply_target, get_cmd('uptime'));
    }
    elsif ($msg =~ /^!df\b/i) {
        my @lines = get_cmd_lines('df -h', 5);
        my @inodes = get_cmd_lines('df -i', 3);
        reply_lines($server, $reply_target, \@lines);
        reply($server, $reply_target, "--- Inodes ---");
        reply_lines($server, $reply_target, \@inodes);
        
        # Dodatkowe informacje o dyskach
        my $lsblk = get_cmd('lsblk -o NAME,SIZE,TYPE,MOUNTPOINT | grep -E "(sd|mmc|nvme)"');
        if ($lsblk) {
            reply($server, $reply_target, "--- Urządzenia blokowe ---");
            reply($server, $reply_target, $lsblk);
        }
    }
    elsif ($msg =~ /^!dfdetail\b/i) {
        my @lines = get_cmd_lines('df -h', 5);
        reply_lines($server, $reply_target, \@lines);
        
        # Dodatkowe statystyki dysków
        my $iostat = get_cmd('iostat -x 1 1 2>/dev/null | tail -3');
        if ($iostat) {
            reply($server, $reply_target, "--- I/O Statystyki ---");
            reply($server, $reply_target, $iostat);
        }
        
        # SMART info dla dysków (jeśli dostępne)
        my $smart = get_cmd('smartctl -a /dev/sda 2>/dev/null | grep -E "(Model|Serial|Power_On|Temperature)" | head -4');
        if ($smart) {
            reply($server, $reply_target, "--- SMART Info ---");
            reply($server, $reply_target, $smart);
        }
    }
    elsif ($msg =~ /^!network\b/i) {
        my $ip = get_cmd('hostname -I');
        my $hostname = get_cmd('hostname');
        my $connections = get_cmd('ss -tuln | wc -l');
        my $established = get_cmd('ss -tuln | grep ESTAB | wc -l');
        
        reply($server, $reply_target, "Hostname: $hostname");
        reply($server, $reply_target, "IP: $ip");
        reply($server, $reply_target, "Połączenia: $connections (ESTAB: $established)");
        
        # Interfejsy sieciowe
        my @interfaces = get_cmd_lines('ip addr show | grep -E "^[0-9]+:|inet " | head -6', 6);
        reply($server, $reply_target, "--- Interfejsy ---");
        reply_lines($server, $reply_target, \@interfaces);
    }
    elsif ($msg =~ /^!load\b/i) {
        my $load = get_cmd('uptime');
        my $cpu_count = get_cmd('nproc');
        my $cpu_usage = get_cmd("top -bn1 | grep 'Cpu(s)' | awk '{print \$2}' | cut -d'%' -f1");
        
        reply($server, $reply_target, "Load: $load");
        reply($server, $reply_target, "CPU cores: $cpu_count");
        reply($server, $reply_target, "CPU usage: ${cpu_usage}%");
        
        # Szczegółowe obciążenie
        my @loadavg = get_cmd_lines('cat /proc/loadavg', 1);
        reply($server, $reply_target, "Load average: $loadavg[0]");
    }
    elsif ($msg =~ /^!processes\b/i) {
        my @cpu_top = get_cmd_lines('ps aux --sort=-%cpu | head -4', 4);
        my @mem_top = get_cmd_lines('ps aux --sort=-%mem | head -4', 4);
        
        reply($server, $reply_target, "--- Top CPU ---");
        reply_lines($server, $reply_target, \@cpu_top);
        reply($server, $reply_target, "--- Top RAM ---");
        reply_lines($server, $reply_target, \@mem_top);
    }
    elsif ($msg =~ /^!diskio\b/i) {
        my $iostat = get_cmd('iostat 1 1 2>/dev/null');
        if ($iostat) {
            reply($server, $reply_target, "--- Disk I/O ---");
            reply($server, $reply_target, $iostat);
        } else {
            # Alternatywna metoda
            my $io_stats = get_cmd('cat /proc/diskstats | grep -E "(sd|mmc)" | head -3');
            reply($server, $reply_target, "--- Disk Stats ---");
            reply($server, $reply_target, $io_stats);
        }
    }
    elsif ($msg =~ /^!logs\b/i) {
        my @systemd_logs = get_cmd_lines('journalctl --no-pager -n 5 --no-hostname', 5);
        reply($server, $reply_target, "--- Ostatnie logi systemd ---");
        reply_lines($server, $reply_target, \@systemd_logs);
        
        # Sprawdź czy są błędy
        my @errors = get_cmd_lines('journalctl --no-pager -p err -n 3 --no-hostname', 3);
        if (@errors) {
            reply($server, $reply_target, "--- Ostatnie błędy ---");
            reply_lines($server, $reply_target, \@errors);
        }
    }
    elsif ($msg =~ /^!free\b/i) {
        my @lines = get_cmd_lines('free -h', 3);
        reply_lines($server, $reply_target, \@lines);
    }
    elsif ($msg =~ /^!uname\b/i) {
        reply($server, $reply_target, get_cmd('uname -a'));
    }
    elsif ($msg =~ /^!temp\b/i) {
        my $temp = get_cmd('cat /sys/class/thermal/thermal_zone0/temp');
        if ($temp =~ /^\d+$/) {
            $temp = sprintf("%.1f°C", $temp/1000);
        }
        reply($server, $reply_target, "CPU temp: $temp");
    }
    elsif ($msg =~ /^!whoami\b/i) {
        reply($server, $reply_target, get_cmd('whoami'));
    }
    elsif ($msg =~ /^!date\b/i) {
        reply($server, $reply_target, get_cmd('date'));
    }
    elsif ($msg =~ /^!ip\b/i) {
        reply($server, $reply_target, get_cmd("hostname -I"));
    }
    elsif ($msg =~ /^!lastlog\b/i) {
        my @lines = get_cmd_lines('last -n 3', 3);
        reply_lines($server, $reply_target, \@lines);
    }
    elsif ($msg =~ /^!ps\b/i) {
        my @lines = get_cmd_lines('ps aux --sort=-%mem | head -4', 4);
        reply_lines($server, $reply_target, \@lines);
    }
    elsif ($msg =~ /^!diskusage\b/i) {
        my @lines = get_cmd_lines('du -sh /home/<USER>/dev/null | sort -hr | head -3', 3);
        reply_lines($server, $reply_target, \@lines);
    }
    elsif ($msg =~ /^!uptimefull\b/i) {
        my $uptime = get_cmd('uptime -p');
        my $since = get_cmd('uptime -s');
        reply($server, $reply_target, "Uptime: $uptime (od $since)");
    }
    elsif ($msg =~ /^!services\b/i) {
        my @lines = get_cmd_lines('systemctl list-units --type=service --state=running | head -8', 8);
        reply_lines($server, $reply_target, \@lines);
    }
    elsif ($msg =~ /^!ram\b/i or $msg =~ /^!free\b/i) {
        my @lines = get_cmd_lines('free -h', 10);
        my $vmstat = get_cmd('vmstat 1 2 | tail -1');
        reply_lines($server, $reply_target, \@lines);
        reply($server, $reply_target, "vmstat: $vmstat");
    }
    elsif ($msg =~ /^!aptupdate\b/i) {
        my @lines = get_apt_updates();
        reply_lines($server, $reply_target, \@lines);
    }
    elsif ($msg =~ /^!help\b/i) {
        send_help_notice($server, $nick);
    }
    # Dodaj kolejne komendy poniżej wg schematu:
    # elsif ($msg =~ /^!twojakomenda\b/i) {
    #     reply($server, $reply_target, $nick, get_cmd('twoje polecenie'));
    # }
}

sub send_help_notice {
    my ($server, $nick) = @_;
    foreach my $line (@help_lines) {
        $server->command("NOTICE $nick $line");
    }
}

sub get_cmd {
    my ($cmd) = @_;
    my $output = qx($cmd 2>&1);
    $output =~ s/[\r\n]+/ | /g; # Jedna linia
    $output =~ s/\s+\|\s+$//;   # Usuń trailing separator
    return $output;
}

sub get_cmd_lines {
    my ($cmd, $max_lines) = @_;
    my @lines = qx($cmd 2>&1);
    chomp @lines;
    @lines = @lines[0..$max_lines-1] if @lines > $max_lines;
    return @lines;
}

sub reply_lines {
    my ($server, $target, $lines_ref) = @_;
    foreach my $line (@$lines_ref) {
        $server->command("MSG $target $line");
    }
}

sub reply {
    my ($server, $target, $msg) = @_;
    $server->command("MSG $target $msg");
}

Irssi::print("Script rpi4_tahio.pl loaded."); 

sub get_apt_updates {
    my @output = qx(apt list --upgradable 2>/dev/null);
    chomp @output;
    my @updates = grep { !/^Listing/ && /\[upgradable from:/ } @output;
    my $count = scalar @updates;
    my @result;
    if ($count == 0) {
        push @result, "Brak dostępnych aktualizacji.";
    } else {
        push @result, "Dostępnych aktualizacji: $count";
        foreach my $line (@updates[0..($count > 2 ? 1 : $count-1)]) {
            $line =~ s/\/.*?\s+/\: /; # czytelniej
            push @result, $line;
        }
        push @result, "...więcej" if $count > 2;
    }
    return @result;
} 