use strict;
use Irssi;
use vars qw($VERSION %IRSSI);

$VERSION = "1.00";
%IRSSI = (
    authors     => 'Your Name',
    contact     => '<EMAIL>',
    name        => 'ctcp_version_response',
    description => 'Custom response to CTCP VERSION requests',
    license     => 'Public Domain',
    url         => 'http://irssi.org/'
);

sub ctcp_version_handler {
    my ($server, $data, $nick, $address, $target) = @_;
    
    my $response = "I am a power bot on IRCnet! Join us on IRCnet2!";
    
    $server->send_message_ctcp($nick, "NOTICE", "VERSION $response");
    Irssi::signal_stop();
}

Irssi::signal_add('ctcp msg version', 'ctcp_version_handler');
