use Irssi;
use Irssi::TextUI;
use strict;

use vars qw($VERSION %IRSSI);

$VERSION="0.2.1";
%IRSSI = (
	authors=> 'BC-bd',
	contact=> '<EMAIL>',
	name=> 'rotator',
	description=> 'Displaye a small, changeing statusbar item to show irssi is still running',
	sbitems=> 'rotator',
	license=> 'GPL v2',
	url=> 'https://bc-bd.org/svn/repos/irssi/trunk/',
);

# rotator Displaye a small, changeing statusbar item to show irssi is still running
# for irssi 0.8.4 by <EMAIL>
#
#########
# USAGE
###
# 
# To use this script type f.e.:
#
#		/statusbar window add -after more -alignment right rotator
#
# For more info on statusbars read the docs for /statusbar
#
#########
# OPTIONS
#########
#
# /set rotator_seperator <char>
# 	The character that is used to split the string in elements.
#
# /set rotator_chars <string>
# 	The string to display. Examples are:
#
# 		/set rotator_chars . o 0 O
