#!/usr/bin/perl
use strict;
use warnings;
use utf8;
use open ':std', ':encoding(UTF-8)';
binmode STDOUT, ':encoding(UTF-8)';
binmode STDERR, ':encoding(UTF-8)';
use Irssi;
BEGIN {
    eval { require JSON::PP; JSON::PP->import(); 1 } or do {
        Irssi::print("YouTube Monitor: Brak modułu JSON::PP! Zainstaluj go przez CPAN.");
        die "Brak JSON::PP";
    };
    eval { require LWP::UserAgent; require HTTP::Request; 1 } or do {
        Irssi::print("YouTube Monitor: Brak modułu LWP::UserAgent! Zainstaluj go przez CPAN.");
        die "Brak LWP::UserAgent";
    };
}
use vars qw($VERSION %IRSSI);

$VERSION = "2.6-debug";
%IRSSI = (
    authors     => 'yooz',
    contact     => '<EMAIL>',
    name        => 'youtube_live_monitor_enhanced',
    description => 'Monitoruje kanał<PERSON> YouTube pod kątem live streamów',
    license     => 'GPL',
    changed     => 'Fri Jul 25 2025'
);

# Konfiguracja - lista kanałów do monitorowania
my @youtube_channels = (
    {
        name => 'Gemini',
        url => 'https://www.youtube.com/@geeminiii/live',
        irc_channel => '#contempt'
    },
    {
        name => 'K6A6T6',
        url => 'https://www.youtube.com/@K6A6T6-x6g/live',
        irc_channel => '#contempt'
    }
    # Dodaj więcej kanałów tutaj:
    # {
    #     name => 'nazwa_kanalu',
    #     url => 'https://www.youtube.com/@nazwa_kanalu/live',
    #     irc_channel => '#kanal_irc'
    # },
);

my $check_interval = 300; # 5 minut w sekundach
my $target_network = "";

# Opcjonalne: YouTube API key (jeśli masz)
my $youtube_api_key = 'AIzaSyBvCm0sXaE1GuzCqropcAHya7Q-VrBa5b0'; # Wstaw swój API key jeśli masz

# Zmienne globalne dla każdego kanału
my %channel_status; # Hash przechowujący status każdego kanału
my $timeout_tag;
my $api_call_count = 0;
my $last_api_call_time = 0;
my $api_call_interval = 600; # 10 minut między wywołaniami API
my $max_api_calls_per_day = 1000; # Limit dzienny

# Inicjalizacja statusu dla każdego kanału
sub init_channel_status {
    for my $channel (@youtube_channels) {
        $channel_status{$channel->{name}} = {
            last_live_status => 0,
            last_video_id => "",
            last_check_time => 0
        };
    }
}

sub decode_unicode {
    my $text = shift;
    return "" unless defined $text;
    $text =~ s/\\u([0-9a-fA-F]{4})/chr(hex($1))/ge;
    $text =~ s/\\n/\n/g;
    $text =~ s/\\r/\r/g;
    $text =~ s/\\t/\t/g;
    $text =~ s/\\"/"/g;
    $text =~ s/\\\\/\\/g;
    return $text;
}

# Rekurencyjnie szukaj live badges w JSON
sub find_live_badges {
    my ($obj, $found_ref) = @_;
    if (ref($obj) eq 'HASH') {
        for my $key (keys %$obj) {
            if ($key eq 'badges' && ref($obj->{$key}) eq 'ARRAY') {
                for my $badge (@{$obj->{$key}}) {
                    if (ref($badge) eq 'HASH' &&
                        exists $badge->{metadataBadgeRenderer} &&
                        exists $badge->{metadataBadgeRenderer}->{style} &&
                        $badge->{metadataBadgeRenderer}->{style} eq "BADGE_STYLE_TYPE_LIVE_NOW") {
                        $$found_ref = 1;
                        return;
                    }
                }
            } elsif (ref($obj->{$key}) eq 'HASH' || ref($obj->{$key}) eq 'ARRAY') {
                find_live_badges($obj->{$key}, $found_ref);
            }
        }
    } elsif (ref($obj) eq 'ARRAY') {
        for my $item (@$obj) {
            find_live_badges($item, $found_ref);
        }
    }
}

sub check_all_channels {
    Irssi::print("YouTube Monitor: Sprawdzam wszystkie kanały...");

    for my $channel (@youtube_channels) {
        check_single_channel($channel);
    }
}

sub check_single_channel {
    my ($channel) = @_;
    my $channel_name = $channel->{name};
    my $channel_url = $channel->{url};

    Irssi::print("YouTube Monitor: Sprawdzam kanał $channel_name...");

    # Użyj LWP::UserAgent zamiast AnyEvent::HTTP
    my $ua = LWP::UserAgent->new;
    $ua->timeout(15);
    $ua->default_header('User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36');
    $ua->default_header('Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8');
    $ua->default_header('Accept-Language' => 'pl-PL,pl;q=0.9,en;q=0.8');
    $ua->default_header('Accept-Encoding' => 'gzip, deflate, br');
    $ua->default_header('Connection' => 'keep-alive');
    $ua->default_header('Upgrade-Insecure-Requests' => '1');
    $ua->default_header('Sec-Fetch-Dest' => 'document');
    $ua->default_header('Sec-Fetch-Mode' => 'navigate');
    $ua->default_header('Sec-Fetch-Site' => 'none');
    $ua->default_header('Cache-Control' => 'no-cache');
    $ua->default_header('Cookie' => 'CONSENT=YES+42');

    my $request = HTTP::Request->new(GET => $channel_url);
    my $response = $ua->request($request);

    if (!$response->is_success) {
        Irssi::print("YouTube Monitor [$channel_name]: Błąd HTTP: " . $response->status_line);
        return;
    }

    my $content = $response->decoded_content;
    my $final_url = $response->request->uri->as_string;

    # Sprawdź czy było przekierowanie
    if ($final_url ne $channel_url) {
        Irssi::print("YouTube Monitor [$channel_name]: Przekierowanie do $final_url");
    }

    _analyze_live_stream($channel, $content, $final_url);
}

sub _analyze_live_stream {
    my ($channel, $content, $url) = @_;
    my $channel_name = $channel->{name};
    my $irc_channel = $channel->{irc_channel};

    my $is_live = 0;
    my $stream_title = "";
    my $live_url = "";
    my $video_id = "";
    my $debug_reason = "";

    # Wyciągnij video_id z URL jeśli to /watch?v=...
    if ($url =~ /v=([a-zA-Z0-9_-]{11})/) {
        $video_id = $1;
        $live_url = "https://youtu.be/$video_id";
        Irssi::print("YouTube Monitor [$channel_name]: Wykryto video_id: $video_id");
    }

    # Jeśli nie ma video_id w URL, spróbuj wyciągnąć z content
    if (!$video_id && $content =~ /"watch\?v=([a-zA-Z0-9_-]{11})"/) {
        $video_id = $1;
        $live_url = "https://youtu.be/$video_id";
        Irssi::print("YouTube Monitor [$channel_name]: Wyciągnięto video_id z content: $video_id");
    }

    # Metoda 1: Sprawdź czy strona zawiera live stream
    if ($content =~ /"isLiveContent":\s*true/i) {
        $is_live = 1;
        $debug_reason = 'isLiveContent:true found';
    }

    # Metoda 2: Sprawdź live badges
    if (!$is_live && $content =~ /"BADGE_STYLE_TYPE_LIVE_NOW"/i) {
        $is_live = 1;
        $debug_reason = 'LIVE_NOW badge found';
    }

    # Metoda 3: Sprawdź live indicator w tekście
    if (!$is_live && $content =~ /"liveStreamingDetails"/i) {
        $is_live = 1;
        $debug_reason = 'liveStreamingDetails found';
    }

    # Metoda 4: Sprawdź meta tagi
    if (!$is_live) {
        if ($content =~ /<meta[^>]+property="og:video:url"[^>]+content="[^"]*\/watch\?v=([a-zA-Z0-9_-]{11})[^"]*"/i &&
            $content =~ /<meta[^>]+name="title"[^>]+content="[^"]*LIVE[^"]*"/i) {
            $is_live = 1;
            $debug_reason = 'meta:og:video + title:LIVE';
            $video_id = $1 unless $video_id;
            $live_url = "https://youtu.be/$video_id" if $video_id;
        }
    }

    # Metoda 5: Sprawdź live indicator w tekście strony
    if (!$is_live && $content =~ /"LIVE"/i && $content =~ /"watch\?v=([a-zA-Z0-9_-]{11})"/i) {
        $is_live = 1;
        $debug_reason = 'LIVE text + video ID found';
        $video_id = $1 unless $video_id;
        $live_url = "https://youtu.be/$video_id" if $video_id;
    }

    # Metoda 5.5: Sprawdź live indicator w tekście strony (uproszczona)
    if (!$is_live && $content =~ /"LIVE"/i) {
        $is_live = 1;
        $debug_reason = 'LIVE text found in page';
        # Spróbuj wyciągnąć video_id z URL lub content
        if (!$video_id && $url =~ /v=([a-zA-Z0-9_-]{11})/) {
            $video_id = $1;
            $live_url = "https://youtu.be/$video_id";
        }
        if (!$video_id && $content =~ /"watch\?v=([a-zA-Z0-9_-]{11})"/) {
            $video_id = $1;
            $live_url = "https://youtu.be/$video_id";
        }
    }

    # Metoda 6: Sprawdź live indicator w HTML
    if (!$is_live && $content =~ /<span[^>]*>LIVE<\/span>/i) {
        $is_live = 1;
        $debug_reason = 'LIVE span found in HTML';
    }

    # Metoda 7: Sprawdź live indicator w JSON
    if (!$is_live && $content =~ /"liveBroadcastDetails"/i) {
        $is_live = 1;
        $debug_reason = 'liveBroadcastDetails found';
    }

    # Metoda 7.5: Sprawdź czy to nie jest strona offline
    if ($content =~ /"offlineMessage"/i) {
        $is_live = 0;
        $debug_reason = 'offlineMessage found - channel is offline';
    }

    # Metoda 7.7: Sprawdź czy stream się zakończył
    if ($content =~ /"isLiveContent":\s*false/i) {
        $is_live = 0;
        $debug_reason = 'isLiveContent:false - stream ended';
    }

    # Metoda 7.6: Sprawdź live indicator w tekście (bardzo ogólna metoda)
    if (!$is_live && $content =~ /live.*stream/i && !$content =~ /offline/i && !$content =~ /"isLiveContent":\s*false/i) {
        $is_live = 1;
        $debug_reason = 'live stream text found';
    }

    # Metoda 8: Parsowanie ytInitialData (nowa metoda)
    if (!$is_live && $content =~ /(?:var |window\[\"'?|window\.)ytInitialData\s*=\s*({.*?});/s) {
        my $json_str = $1;
        eval {
            my $json = JSON::PP->new->decode($json_str);

            # Sprawdź videoDetails
            if (exists $json->{videoDetails}) {
                my $video_details = $json->{videoDetails};
                if (exists $video_details->{isLiveContent} && $video_details->{isLiveContent}) {
                    $is_live = 1;
                    $debug_reason = 'videoDetails->isLiveContent:true';
                    $stream_title = $video_details->{title} || "";
                    $stream_title = decode_unicode($stream_title);
                    $video_id = $video_details->{videoId} || $video_id;
                    $live_url = "https://youtu.be/$video_id" if $video_id;
                }
            }

            # Sprawdź live badges w różnych miejscach
            if (!$is_live) {
                my $live_badge_found = 0;
                find_live_badges($json, \$live_badge_found);
                if ($live_badge_found) {
                    $is_live = 1;
                    $debug_reason = 'LIVE_NOW badge found in JSON';
                }
            }
        };
        if ($@) {
            Irssi::print("YouTube Monitor [$channel_name]: Błąd parsowania JSON: $@");
        }
    }

    # Metoda 9: Jeśli masz API key, użyj YouTube API (jak w nyt.pl) - OSZCZĘDNIE
    if ($youtube_api_key && $video_id) {
        my $current_time = time();

        # Sprawdź czy możemy użyć API (limit czasowy i dzienny)
        if ($current_time - $last_api_call_time >= $api_call_interval &&
            $api_call_count < $max_api_calls_per_day) {

            $api_call_count++;
            $last_api_call_time = $current_time;

            Irssi::print("YouTube Monitor [$channel_name]: Używam API (wywołanie #$api_call_count dzisiaj)");

            # Użyj LWP::UserAgent dla API call
            my $ua_api = LWP::UserAgent->new;
            $ua_api->timeout(15);

            my $api_url = "https://www.googleapis.com/youtube/v3/videos?id=$video_id&key=$youtube_api_key&part=snippet,status,liveStreamingDetails";
            my $api_request = HTTP::Request->new(GET => $api_url);
            my $api_response = $ua_api->request($api_request);

            if ($api_response->is_success) {
                eval {
                    my $api_data = JSON::PP->new->decode($api_response->decoded_content);
                    if ($api_data->{items} && @{$api_data->{items}}) {
                        my $item = $api_data->{items}->[0];
                        if (exists $item->{status}->{uploadStatus} &&
                            $item->{status}->{uploadStatus} eq 'processed' &&
                            exists $item->{liveStreamingDetails}) {
                            $is_live = 1;
                            $debug_reason = 'YouTube API confirmed live';
                            $stream_title = $item->{snippet}->{title} || $stream_title;
                            $stream_title = decode_unicode($stream_title);
                        }
                    }
                };
                if ($@) {
                    Irssi::print("YouTube Monitor [$channel_name]: Błąd parsowania API JSON: $@");
                }
            } else {
                Irssi::print("YouTube Monitor [$channel_name]: Błąd API HTTP: " . $api_response->status_line);
            }
        } else {
            if ($api_call_count >= $max_api_calls_per_day) {
                Irssi::print("YouTube Monitor [$channel_name]: Osiągnięto dzienny limit API ($max_api_calls_per_day)");
            } else {
                my $remaining = $api_call_interval - ($current_time - $last_api_call_time);
                Irssi::print("YouTube Monitor [$channel_name]: API używane niedawno, następne za ${remaining}s");
            }
        }
    }

    _finalize_live_check($channel, $is_live, $stream_title, $live_url, $video_id, $debug_reason, $content, $url);
}

sub _finalize_live_check {
    my ($channel, $is_live, $stream_title, $live_url, $video_id, $debug_reason, $content, $url) = @_;
    my $channel_name = $channel->{name};
    my $irc_channel = $channel->{irc_channel};

    if (!$is_live) {
        Irssi::print("YouTube Monitor [$channel_name]: Nie wykryto live streamu. [debug: $debug_reason]");
        # Dodatkowe debugowanie
        if ($content =~ /"isLiveContent":\s*false/i) {
            Irssi::print("YouTube Monitor [$channel_name]: DEBUG: Znaleziono isLiveContent:false");
        }
        if ($content =~ /"LIVE"/i) {
            Irssi::print("YouTube Monitor [$channel_name]: DEBUG: Znaleziono tekst 'LIVE' w stronie");
            # Sprawdź czy to nie jest fałszywy pozytyw
            if ($content =~ /"offlineMessage"/i) {
                Irssi::print("YouTube Monitor [$channel_name]: DEBUG: Ale znaleziono też 'offlineMessage' - może to fałszywy pozytyw");
            }
        }
        if ($content =~ /"watch\?v=([a-zA-Z0-9_-]{11})"/i) {
            Irssi::print("YouTube Monitor [$channel_name]: DEBUG: Znaleziono video ID w stronie");
        }
    } else {
        Irssi::print("YouTube Monitor [$channel_name]: Wykryto live stream! [debug: $debug_reason]");
        Irssi::print("YouTube Monitor [$channel_name]: Tytuł: $stream_title") if $stream_title;
        Irssi::print("YouTube Monitor [$channel_name]: URL: $live_url") if $live_url;
    }

    my $status = $channel_status{$channel_name};

    # Debugowanie statusu
    Irssi::print("YouTube Monitor [$channel_name]: DEBUG: is_live=$is_live, last_live_status=$status->{last_live_status}");

    if ($is_live && !$status->{last_live_status}) {
        $status->{last_live_status} = 1;
        $status->{last_video_id} = $video_id if $video_id;
        my $msg = "🔴 LIVE $channel_name : ";
        if ($live_url) {
            $msg .= $live_url;
        } elsif ($video_id) {
            $msg .= "https://youtu.be/$video_id";
        } else {
            $msg .= $url;
        }
        if ($stream_title) {
            $msg = "🔴 LIVE $channel_name : $stream_title | " . $msg;
        }
        send_notification($irc_channel, $msg);
        Irssi::print("YouTube Monitor [$channel_name]: Wykryto nowy live stream!");
    } elsif (!$is_live && $status->{last_live_status}) {
        Irssi::print("YouTube Monitor [$channel_name]: DEBUG: Stream się zakończył, wysyłam powiadomienie");
        $status->{last_live_status} = 0;
        $status->{last_video_id} = "";
        send_notification($irc_channel, "🟢 LIVE $channel_name zakończony!");
        Irssi::print("YouTube Monitor [$channel_name]: Live stream zakończony.");
    } else {
        if ($is_live) {
            Irssi::print("YouTube Monitor [$channel_name]: DEBUG: Stream już był wykryty wcześniej");
        } else {
            Irssi::print("YouTube Monitor [$channel_name]: DEBUG: Stream nadal nieaktywny");
        }
    }

    $status->{last_check_time} = time();

    # Sprawdź czy wszystkie kanały zostały sprawdzone
    my $all_checked = 1;
    for my $ch (@youtube_channels) {
        if ($channel_status{$ch->{name}}->{last_check_time} == 0) {
            $all_checked = 0;
            last;
        }
    }

    # Jeśli wszystkie kanały sprawdzone, zaplanuj następne sprawdzenie
    if ($all_checked) {
        schedule_next_check();
    }
}

sub send_notification {
    my ($irc_channel, $message) = @_;
    my $server;
    if ($target_network) {
        $server = Irssi::server_find_tag($target_network);
    } else {
        $server = Irssi::active_server();
    }
    if (!$server) {
        Irssi::print("YouTube Monitor: Nie można znaleźć serwera!");
        return;
    }
    $server->command("MSG $irc_channel $message");
    Irssi::print("YouTube Monitor: Wysłano wiadomość na $irc_channel: $message");
}

# Automatyczne uruchomienie
init_channel_status();
$api_call_count = 0;
$last_api_call_time = 0;

# Ustaw last_check_time na 0 dla wszystkich kanałów
for my $channel (@youtube_channels) {
    $channel_status{$channel->{name}}->{last_check_time} = 0;
}

check_all_channels();

# Reset licznika API każdego dnia o północy
sub reset_daily_api_count {
    $api_call_count = 0;
    Irssi::print("YouTube Monitor: Reset dziennego licznika API");
    Irssi::timeout_add(24 * 60 * 60 * 1000, 'reset_daily_api_count', undef); # 24 godziny
}
Irssi::timeout_add(24 * 60 * 60 * 1000, 'reset_daily_api_count', undef); # Pierwszy reset za 24h

# Timer dla sprawdzania wszystkich kanałów
sub schedule_next_check {
    Irssi::timeout_remove($timeout_tag) if $timeout_tag;
    $timeout_tag = Irssi::timeout_add($check_interval * 1000, 'check_all_channels', undef);
}

sub cleanup {
    if ($timeout_tag) {
        Irssi::timeout_remove($timeout_tag);
        $timeout_tag = undef;
    }
}

Irssi::print("YouTube Monitor Enhanced v$VERSION załadowany!");
Irssi::print("Liczba monitorowanych kanałów: " . scalar(@youtube_channels));
for my $channel (@youtube_channels) {
    Irssi::print("  - $channel->{name} -> $channel->{irc_channel}");
}
if ($youtube_api_key) {
    Irssi::print("YouTube API: włączone (max $max_api_calls_per_day wywołań/dzień, co $api_call_interval sekund)");
} else {
    Irssi::print("YouTube API: wyłączone (dodaj API key dla lepszej detekcji)");
}

END {
    cleanup();
}