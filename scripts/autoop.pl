use strict;
use vars qw($VERSION %IRSSI);

use Irssi;

$VERSION = "2.1";
%IRSSI = (
    authors     => "yooz",
    name        => "AutoOp",
    description => "Automatyczne nadawanie statusu operatora",
    license     => "GPL",
);

# Lista ownerów
my @owners = ('*!<EMAIL>', '*!<EMAIL>');

# Hash przechowujący listę autoop: host => [kanały]
my %autoop_list = ();

# Funkcja sprawdzająca czy użytkownik jest ownerem
sub is_owner {
    my $hostmask = shift;
    Irssi::print("Sprawdzam owner: $hostmask");
    foreach my $owner (@owners) {
        if (match_hostmask($owner, $hostmask)) {
            Irssi::print("TAK - jest ownerem");
            return 1;
        }
    }
    Irssi::print("NIE - nie jest ownerem");
    return 0;
}

# Funkcja dopasowywania hostmaski
sub match_hostmask {
    my ($pattern, $hostmask) = @_;
    my $regex_pattern = $pattern;
    $regex_pattern =~ s/\./\\./g;
    $regex_pattern =~ s/\*/.*?/g;
    $regex_pattern =~ s/\?/./g;
    my $result = $hostmask =~ /^$regex_pattern$/i;
    Irssi::print("Match: $pattern vs $hostmask = " . ($result ? "TAK" : "NIE"));
    return $result;
}

# Obsługa komendy !addop - POPRAWIONA Z LEPSZYM REGEX
sub handle_addop {
    my ($server, $msg, $nick, $address, $target) = @_;
    
    Irssi::print("DEBUG: handle_addop wywołana: '$msg' od $nick!$address na $target");

    # Sprawdź czy użytkownik jest ownerem
    my $hostmask = "$nick!$address";
    unless (is_owner($hostmask)) {
        Irssi::print("DEBUG: Nie owner - wychodzę");
        return;
    }

    # Parsuj komendę - POPRAWIONY REGEX (obsługuje dodatkowe spacje)
    Irssi::print("DEBUG: Sprawdzam regex dla: '$msg'");
    if ($msg =~ /^!addop\s+(\S+)\s*$/i) {
        my $target_nick = $1;
        Irssi::print("DEBUG: Parsowanie udane - target: '$target_nick'");

        # Sprawdź czy użytkownik jest na kanale
        my $target_channel = $server->channel_find($target);
        if ($target_channel) {
            Irssi::print("DEBUG: Kanał znaleziony: $target");
            my $target_user = $target_channel->nick_find($target_nick);
            if ($target_user) {
                Irssi::print("DEBUG: Użytkownik znaleziony na kanale");
                my $target_address = $target_user->{host};
                my $target_hostmask = "*!*\@$target_address";
                
                Irssi::print("DEBUG: Hostmask: $target_hostmask");
                
                # Dodaj do autoop
                if (!exists $autoop_list{$target_hostmask}) {
                    $autoop_list{$target_hostmask} = [];
                    Irssi::print("DEBUG: Utworzono nową listę dla $target_hostmask");
                }
                
                # Sprawdź czy kanał już nie jest na liście
                my $found = 0;
                foreach my $chan (@{$autoop_list{$target_hostmask}}) {
                    if (lc($chan) eq lc($target)) {
                        $found = 1;
                        last;
                    }
                }
                
                if (!$found) {
                    push @{$autoop_list{$target_hostmask}}, $target;
                    Irssi::print("DEBUG: Dodano kanał $target do listy");
                    save_autoop_list();
                    $server->command("MSG $target $target_nick dodany do autoop");
                    
                    # Natychmiast nadaj opa
                    if (!$target_user->{op}) {
                        Irssi::print("DEBUG: Nadaję op dla $target_nick");
                        $server->command("MODE $target +o $target_nick");
                    } else {
                        Irssi::print("DEBUG: $target_nick już ma opa");
                    }
                } else {
                    Irssi::print("DEBUG: Kanał już na liście");
                    $server->command("MSG $target $target_nick już jest na liście autoop");
                }
            } else {
                Irssi::print("DEBUG: Użytkownik $target_nick nie znaleziony na kanale");
                $server->command("MSG $target Użytkownik $target_nick nie jest na kanale");
            }
        } else {
            Irssi::print("DEBUG: Kanał $target nie znaleziony");
        }
    } else {
        Irssi::print("DEBUG: Komenda nie pasuje do wzorca: '$msg'");
        Irssi::print("DEBUG: Długość wiadomości: " . length($msg));
        Irssi::print("DEBUG: Czy zaczyna się od !addop: " . ($msg =~ /^!addop/i ? "TAK" : "NIE"));
        Irssi::print("DEBUG: Czy ma spację po addop: " . ($msg =~ /^!addop\s/i ? "TAK" : "NIE"));
        Irssi::print("DEBUG: Czy kończy się na \S+: " . ($msg =~ /\S+$/ ? "TAK" : "NIE"));
    }
}

# Obsługa join użytkownika
sub handle_join {
    my ($server, $channel, $nick, $address) = @_;
    my $hostmask = "$nick!$address";
    
    Irssi::print("DEBUG: JOIN - $nick!$address dołączył do $channel");

    # Sprawdź ownerów
    if (is_owner($hostmask)) {
        Irssi::print("DEBUG: Owner dołączył - nadaję op");
        $server->command("MODE $channel +o $nick");
        return;
    }

    # Sprawdź autoop
    Irssi::print("DEBUG: Sprawdzam autoop dla $hostmask");
    foreach my $pattern (keys %autoop_list) {
        Irssi::print("DEBUG: Sprawdzam wzorzec: $pattern");
        if (match_hostmask($pattern, $hostmask)) {
            Irssi::print("DEBUG: Wzorzec pasuje!");
            foreach my $chan (@{$autoop_list{$pattern}}) {
                Irssi::print("DEBUG: Sprawdzam kanał: $chan vs $channel");
                if (lc($chan) eq lc($channel)) {
                    Irssi::print("DEBUG: Kanał pasuje - nadaję op");
                    $server->command("MODE $channel +o $nick");
                    return;
                }
            }
        }
    }
    Irssi::print("DEBUG: Nie znaleziono pasującego wzorca");
}

# Obsługa komendy !listop
sub handle_listop {
    my ($server, $msg, $nick, $address, $target) = @_;

    Irssi::print("DEBUG: handle_listop wywołana");

    if ($msg !~ /^!listop$/i) {
        return;
    }

    my $hostmask = "$nick!$address";
    unless (is_owner($hostmask)) {
        return;
    }

    $server->command("MSG $target === Lista AutoOp ===");

    if (keys %autoop_list == 0) {
        $server->command("MSG $target Lista jest pusta");
        return;
    }

    foreach my $host (sort keys %autoop_list) {
        my $channels = join(', ', @{$autoop_list{$host}});
        $server->command("MSG $target $host -> $channels");
    }
}

# Obsługa komendy !delop
sub handle_delop {
    my ($server, $msg, $nick, $address, $target) = @_;

    if ($msg !~ /^!delop\s+(\S+)$/i) {
        return;
    }

    my $host_to_remove = $1;
    my $hostmask = "$nick!$address";
    unless (is_owner($hostmask)) {
        return;
    }

    if (exists $autoop_list{$host_to_remove}) {
        delete $autoop_list{$host_to_remove};
        save_autoop_list();
        $server->command("MSG $target Usunięto $host_to_remove z listy autoop");
    } else {
        $server->command("MSG $target Host $host_to_remove nie znaleziony na liście");
    }
}

# Funkcja zapisu do pliku
sub save_autoop_list {
    my $config_dir = Irssi::get_irssi_dir();
    my $filename = "$config_dir/aop_hosts";

    Irssi::print("DEBUG: Próba zapisu do $filename");

    open(my $fh, '>', $filename) or do {
        Irssi::print("BŁĄD zapisu: $!");
        return;
    };

    print $fh "# AutoOp list\n";
    my $count = 0;
    foreach my $hostmask (keys %autoop_list) {
        my $channels = join(',', @{$autoop_list{$hostmask}});
        print $fh "$hostmask:$channels\n";
        $count++;
        Irssi::print("DEBUG: Zapisano: $hostmask:$channels");
    }
    close($fh);
    Irssi::print("DEBUG: Zapisano $count wpisów do pliku");
}

# Funkcja wczytania z pliku
sub load_autoop_list {
    my $config_dir = Irssi::get_irssi_dir();
    my $filename = "$config_dir/aop_hosts";

    Irssi::print("DEBUG: Próba wczytania z $filename");

    return unless -f $filename;

    open(my $fh, '<', $filename) or do {
        Irssi::print("BŁĄD odczytu: $!");
        return;
    };

    %autoop_list = ();

    while (my $line = <$fh>) {
        chomp $line;
        next if $line =~ /^\s*$/ || $line =~ /^\s*#/;

        if ($line =~ /^([^:]+):(.+)$/) {
            my $hostmask = $1;
            my @channels = split(',', $2);
            $autoop_list{$hostmask} = \@channels;
            Irssi::print("DEBUG: Wczytano: $hostmask -> " . join(', ', @channels));
        }
    }
    close($fh);
    Irssi::print("DEBUG: Wczytano listę autoop");
}

# Rejestracja sygnałów
Irssi::signal_add('message public', 'handle_addop');
Irssi::signal_add('message public', 'handle_listop');
Irssi::signal_add('message public', 'handle_delop');
Irssi::signal_add('message join', 'handle_join');

# Wczytaj listę przy starcie
load_autoop_list();

Irssi::print("AutoOp script v$VERSION załadowany");
Irssi::print("Komendy: !addop <nick>, !listop, !delop <hostmask>");
Irssi::print("Ownerzy: " . join(', ', @owners));