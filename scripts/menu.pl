use Irssi;
use strict;
use LWP::UserAgent;

# Adresy URL do plików
my $ALIAS_URL = "http://185.243.218.48/aliasy.txt";
my $MENU_URL = "http://185.243.218.48/menu.txt";

# Pobieranie plików z internetu
sub fetch_url {
    my ($url) = @_;
    my $ua = LWP::UserAgent->new;
    my $response = $ua->get($url);

    if ($response->is_success) {
        return $response->decoded_content;
    } else {
        Irssi::print("Błąd podczas pobierania z $url: " . $response->status_line);
        return undef;
    }
}

# Komenda do pobierania i wczytywania aliasów
sub cmd_dejmenu {
    my $content = fetch_url($ALIAS_URL);
    if (defined $content) {
        my @lines = split("\n", $content);
        foreach my $line (@lines) {
            Irssi::command($line);
        }
        Irssi::print("<PERSON>asy zostały wczytane pomyślnie.");
    }
}

# Komenda do pobierania i wyświetlania menu
sub cmd_menu {
    my $content = fetch_url($MENU_URL);
    if (defined $content) {
        Irssi::print($content);
    }
}

Irssi::command_bind('dejmenu', 'cmd_dejmenu');
Irssi::command_bind('menu', 'cmd_menu');
