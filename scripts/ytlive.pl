#!/usr/bin/perl
use strict;
use warnings;
use utf8;
use open ':std', ':encoding(UTF-8)';
binmode STDOUT, ':encoding(UTF-8)';
binmode STDERR, ':encoding(UTF-8)';
use Irssi;
BEGIN {
    eval { require JSON::PP; JSON::PP->import(); 1 } or do {
        Irssi::print("YouTube Monitor: Brak modułu JSON::PP! Zainstaluj go przez CPAN.");
        die "Brak JSON::PP";
    };
    eval { require LWP::UserAgent; require HTTP::Request; 1 } or do {
        Irssi::print("YouTube Monitor: Brak modułu LWP::UserAgent! Zainstaluj go przez CPAN.");
        die "Brak LWP::UserAgent";
    };
}
use vars qw($VERSION %IRSSI);

$VERSION = "3.3-realtime";
%IRSSI = (
    authors     => 'yooz',
    contact     => '<EMAIL>',
    name        => 'youtube_live_monitor_realtime',
    description => 'Monitoruje kanały YouTube pod kątem live streamów w czasie rzeczywistym',
    license     => 'GPL',
    changed     => 'Fri Jul 25 2025'
);

# Konfiguracja - lista kanałów do monitorowania
my @youtube_channels = (
    {
        name => 'Gemini',
        url => 'https://www.youtube.com/@geeminiii/live',
        irc_channel => '#contempt'
    },
    {
        name => 'K6A6T6', 
        url => 'https://www.youtube.com/@K6A6T6-x6g/live',
        irc_channel => '#contempt'
    }
    # Dodaj więcej kanałów tutaj:
    # {
    #     name => 'nazwa_kanalu',
    #     url => 'https://www.youtube.com/@nazwa_kanalu/live',
    #     irc_channel => '#kanal_irc'
    # },
);

my $target_network = "";

# Zmienne globalne dla każdego kanału
my %channel_status; # Hash przechowujący status każdego kanału

# Inicjalizacja statusu dla każdego kanału z rozszerzonymi polami
sub init_channel_status {
    for my $channel (@youtube_channels) {
        $channel_status{$channel->{name}} = {
            last_live_status => 0,
            last_video_id => "",
            last_check_time => 0,
            error_count => 0,
            timeout_tag => undef
        };
    }
}

# Funkcja do obliczania dynamicznego interwału sprawdzania
sub get_check_interval {
    my ($channel_name) = @_;
    my $status = $channel_status{$channel_name};
    
    if ($status->{error_count} > 5) {
        return 30; # 30 sekund po wielu błędach
    } elsif ($status->{last_live_status}) {
        return 1; # 1 sekunda gdy live
    } else {
        return 5; # 5 sekund gdy offline
    }
}

# Optymalizowany UserAgent
sub create_optimized_ua {
    my $ua = LWP::UserAgent->new;
    $ua->timeout(2); # Zmniejszony timeout dla szybszych odpowiedzi
    $ua->default_header('User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36');
    $ua->default_header('Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8');
    $ua->default_header('Accept-Language' => 'pl-PL,pl;q=0.9,en;q=0.8');
    $ua->default_header('Accept-Encoding' => 'gzip, deflate, br');
    $ua->default_header('Connection' => 'close'); # Zamykaj połączenia
    $ua->default_header('Cache-Control' => 'no-cache');
    $ua->default_header('Cookie' => 'CONSENT=YES+42');
    return $ua;
}

sub decode_unicode {
    my $text = shift;
    return "" unless defined $text;
    $text =~ s/\\u([0-9a-fA-F]{4})/chr(hex($1))/ge;
    $text =~ s/\\n/\n/g;
    $text =~ s/\\r/\r/g;
    $text =~ s/\\t/\t/g;
    $text =~ s/\\"/"/g;
    $text =~ s/\\\\/\\/g;
    return $text;
}

# Ulepszona funkcja do wyciągania video ID (bez debug)
sub extract_video_id {
    my ($content, $url) = @_;
    my $video_id = "";
    
    # Metoda 1: Z URL (jeśli już przekierowany do /watch?v=)
    if ($url =~ /[?&]v=([a-zA-Z0-9_-]{11})/) {
        return $1;
    }
    
    # Metoda 2: Z meta tagów
    if ($content =~ /<meta[^>]+property="og:url"[^>]+content="[^"]*\/watch\?v=([a-zA-Z0-9_-]{11})[^"]*"/i) {
        return $1;
    }
    
    # Metoda 3: Z linków w treści
    if ($content =~ /"url":"\/watch\?v=([a-zA-Z0-9_-]{11})"/i) {
        return $1;
    }
    
    # Metoda 4: Z ytInitialData
    if ($content =~ /"videoId":"([a-zA-Z0-9_-]{11})"/i) {
        return $1;
    }
    
    # Metoda 5: Z ogólnych linków watch
    if ($content =~ /\/watch\?v=([a-zA-Z0-9_-]{11})/i) {
        return $1;
    }
    
    # Metoda 6: Z ytInitialData (głębsze parsowanie)
    if ($content =~ /(?:var |window\[\"'?|window\.)ytInitialData\s*=\s*({.*?});/s) {
        my $json_str = $1;
        eval {
            my $json = JSON::PP->new->decode($json_str);
            
            # Sprawdź videoDetails
            if (exists $json->{videoDetails} && exists $json->{videoDetails}->{videoId}) {
                return $json->{videoDetails}->{videoId};
            }
            
            # Sprawdź inne miejsca w JSON
            if (exists $json->{currentVideoEndpoint} && exists $json->{currentVideoEndpoint}->{watchEndpoint} && exists $json->{currentVideoEndpoint}->{watchEndpoint}->{videoId}) {
                return $json->{currentVideoEndpoint}->{watchEndpoint}->{videoId};
            }
        };
        if ($@) {
            Irssi::print("YouTube Monitor: Błąd parsowania JSON dla video ID: $@");
        }
    }
    
    return $video_id;
}

# Rekurencyjnie szukaj live badges w JSON
sub find_live_badges {
    my ($obj, $found_ref) = @_;
    if (ref($obj) eq 'HASH') {
        for my $key (keys %$obj) {
            if ($key eq 'badges' && ref($obj->{$key}) eq 'ARRAY') {
                for my $badge (@{$obj->{$key}}) {
                    if (ref($badge) eq 'HASH' && 
                        exists $badge->{metadataBadgeRenderer} &&
                        exists $badge->{metadataBadgeRenderer}->{style} &&
                        $badge->{metadataBadgeRenderer}->{style} eq "BADGE_STYLE_TYPE_LIVE_NOW") {
                        $$found_ref = 1;
                        return;
                    }
                }
            } elsif (ref($obj->{$key}) eq 'HASH' || ref($obj->{$key}) eq 'ARRAY') {
                find_live_badges($obj->{$key}, $found_ref);
            }
        }
    } elsif (ref($obj) eq 'ARRAY') {
        for my $item (@$obj) {
            find_live_badges($item, $found_ref);
        }
    }
}

# Nowa funkcja sprawdzania kanału w czasie rzeczywistym
sub check_channel_realtime {
    my ($channel) = @_;
    my $channel_name = $channel->{name};
    
    # Sprawdź kanał
    check_single_channel($channel);
    
    # Zaplanuj następne sprawdzenie z dynamicznym interwałem
    my $next_interval = get_check_interval($channel_name) * 1000; # w milisekundach
    
    # Usuń poprzedni timeout jeśli istnieje
    if ($channel_status{$channel_name}->{timeout_tag}) {
        Irssi::timeout_remove($channel_status{$channel_name}->{timeout_tag});
    }
    
    # Dodaj losowe opóźnienie 1-3 sekundy aby uniknąć rate limiting
    my $random_delay = int(rand(2000)) + 1000; # 1-3 sekundy
    $next_interval += $random_delay;
    
    $channel_status{$channel_name}->{timeout_tag} = 
        Irssi::timeout_add($next_interval, 'check_channel_realtime', $channel);
}

sub check_single_channel {
    my ($channel) = @_;
    my $channel_name = $channel->{name};
    my $channel_url = $channel->{url};
    
    # Użyj optymalizowanego UserAgent
    my $ua = create_optimized_ua();
    
    my $request = HTTP::Request->new(GET => $channel_url);
    my $response = $ua->request($request);
    
    if (!$response->is_success) {
        $channel_status{$channel_name}->{error_count}++;
        
        # Pokaż błąd tylko po 3 nieudanych próbach
        if ($channel_status{$channel_name}->{error_count} >= 3) {
            Irssi::print("YouTube Monitor [$channel_name]: Błąd HTTP: " . $response->status_line);
        }
        return;
    }
    
    # Reset licznika błędów po udanym request
    $channel_status{$channel_name}->{error_count} = 0;
    
    my $content = $response->decoded_content;
    my $final_url = $response->request->uri->as_string;
    
    _analyze_live_stream($channel, $content, $final_url);
}

sub _analyze_live_stream {
    my ($channel, $content, $url) = @_;
    my $channel_name = $channel->{name};
    my $irc_channel = $channel->{irc_channel};
    
    my $is_live = 0;
    my $stream_title = "";
    my $video_id = "";
    
    # Ulepszone wyciąganie video ID
    $video_id = extract_video_id($content, $url);
    
    # Szybkie metody detekcji (priorytet na szybkość)
    
    # Metoda 1: Sprawdź czy strona zawiera live stream
    if ($content =~ /"isLiveContent":\s*true/i) {
        $is_live = 1;
    }
    
    # Metoda 2: Sprawdź live badges
    if (!$is_live && $content =~ /"BADGE_STYLE_TYPE_LIVE_NOW"/i) {
        $is_live = 1;
    }
    
    # Metoda 3: Sprawdź live indicator w tekście
    if (!$is_live && $content =~ /"liveStreamingDetails"/i) {
        $is_live = 1;
    }
    
    # Metoda 4: Sprawdź live indicator w tekście strony (uproszczona)
    if (!$is_live && $content =~ /"LIVE"/i) {
        $is_live = 1;
    }
    
    # Metoda 5: Sprawdź czy to nie jest strona offline
    if ($content =~ /"offlineMessage"/i) {
        $is_live = 0;
    }
    
    # Metoda 6: Sprawdź czy stream się zakończył
    if ($content =~ /"isLiveContent":\s*false/i) {
        $is_live = 0;
    }
    
    # Metoda 7: Parsowanie ytInitialData (tylko jeśli potrzebne)
    if (!$is_live && $content =~ /(?:var |window\[\"'?|window\.)ytInitialData\s*=\s*({.*?});/s) {
        my $json_str = $1;
        eval {
            my $json = JSON::PP->new->decode($json_str);
            
            # Sprawdź videoDetails
            if (exists $json->{videoDetails}) {
                my $video_details = $json->{videoDetails};
                if (exists $video_details->{isLiveContent} && $video_details->{isLiveContent}) {
                    $is_live = 1;
                    $stream_title = $video_details->{title} || "";
                    $stream_title = decode_unicode($stream_title);
                    $video_id = $video_details->{videoId} || $video_id;
                }
            }
            
            # Sprawdź live badges w różnych miejscach
            if (!$is_live) {
                my $live_badge_found = 0;
                find_live_badges($json, \$live_badge_found);
                if ($live_badge_found) {
                    $is_live = 1;
                }
            }
        };
        if ($@) {
            Irssi::print("YouTube Monitor [$channel_name]: Błąd parsowania JSON: $@");
        }
    }
    
    _finalize_live_check($channel, $is_live, $stream_title, $video_id, $content, $url);
}

sub _finalize_live_check {
    my ($channel, $is_live, $stream_title, $video_id, $content, $url) = @_;
    my $channel_name = $channel->{name};
    my $irc_channel = $channel->{irc_channel};
    
    my $status = $channel_status{$channel_name};
    
    if ($is_live && !$status->{last_live_status}) {
        $status->{last_live_status} = 1;
        $status->{last_video_id} = $video_id if $video_id;
        
        # Generuj link w formacie https://www.youtube.com/watch?v=
        my $watch_url = "";
        if ($video_id) {
            $watch_url = "https://www.youtube.com/watch?v=$video_id";
        } else {
            $watch_url = $url; # Fallback do oryginalnego URL
        }
        
        my $msg = "🔴 LIVE $channel_name : $watch_url";
        if ($stream_title) {
            $msg = "🔴 LIVE $channel_name : $stream_title | $watch_url";
        }
        
        send_notification($irc_channel, $msg);
    } elsif (!$is_live && $status->{last_live_status}) {
        $status->{last_live_status} = 0;
        $status->{last_video_id} = "";
        send_notification($irc_channel, "🟢 LIVE $channel_name zakończony!");
    }
    
    $status->{last_check_time} = time();
}

sub send_notification {
    my ($irc_channel, $message) = @_;
    my $server;
    if ($target_network) {
        $server = Irssi::server_find_tag($target_network);
    } else {
        $server = Irssi::active_server();
    }
    if (!$server) {
        Irssi::print("YouTube Monitor: Nie można znaleźć serwera!");
        return;
    }
    $server->command("MSG $irc_channel $message");
}

# Funkcja czyszczenia timeoutów
sub cleanup {
    for my $channel (@youtube_channels) {
        my $channel_name = $channel->{name};
        if ($channel_status{$channel_name}->{timeout_tag}) {
            Irssi::timeout_remove($channel_status{$channel_name}->{timeout_tag});
            $channel_status{$channel_name}->{timeout_tag} = undef;
        }
    }
}

# Automatyczne uruchomienie
init_channel_status();

# Uruchom sprawdzanie dla wszystkich kanałów równolegle
for my $channel (@youtube_channels) {
    check_channel_realtime($channel);
}

Irssi::print("YouTube Monitor Realtime v$VERSION załadowany!");
Irssi::print("Liczba monitorowanych kanałów: " . scalar(@youtube_channels));
for my $channel (@youtube_channels) {
    Irssi::print("  - $channel->{name} -> $channel->{irc_channel}");
}
Irssi::print("Monitoring w czasie rzeczywistym: włączony (1-5 sekund interwał)");

END {
    cleanup();
}