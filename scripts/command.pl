use Irssi;
use strict;
use warnings;
use IO::Socket::INET;
use IO::Select;

my $password = 'G181!70!f4j4!';
my $hubhost = '************';
my $hubport = '4446';
Irssi::settings_add_bool('misc', 'hub_print_aw', 0);


sub send_to_hub {
    my ($command) = @_;

    Irssi::print("Wysyłanie komendy do huba: $command");

    my $hub_socket = IO::Socket::INET->new(
        PeerAddr => $hubhost,
        PeerPort => $hubport,
        Proto => 'tcp',
        Blocking => 1
    );

    if (!$hub_socket) {
        Irssi::print("Błąd: Nie można połączyć się z hubem: $!");
        return;
    }

    print $hub_socket "$password $command\n";

    my $select = IO::Select->new();
    $select->add($hub_socket);

    if ($select->can_read(5)) {
        my $first_line = 1;
        while (my $response = <$hub_socket>) {
            chomp $response;
            if ($first_line) {
                if (Irssi::settings_get_bool('hub_print_aw')) {
                    Irssi::active_win()->print("%M Odpowiedź z huba:");
                } else {
                    Irssi::print("%M Odpowiedź z huba:");
                }
                $first_line = 0;
            }
            if (Irssi::settings_get_bool('hub_print_aw')) {
                Irssi::active_win()->print("\x0312" . $response . "\x03");
            } else {
                Irssi::print("\x0312" . $response . "\x03");
            }
        }
    } else {
        Irssi::print("Błąd: Brak odpowiedzi od huba w ciągu 5 sekund.");
    }

    close $hub_socket;
}

#############################
####### Komendy start #######
#############################
Irssi::command_bind('.bots', sub {
    send_to_hub(".bots");
});

Irssi::command_bind('.admins', sub {
    send_to_hub(".admins");
});

Irssi::command_bind('.listall', sub {
    send_to_hub(".listall");
});
Irssi::command_bind('.mjoin', sub {
    my ($data, $server, $witem) = @_;
    if (split(' ', $data) < 1) {
        Irssi::active_win()->print("%MBłąd.%Y Prawidłowe użycie:%W /.mjoin #kanał");
        return;
    }
    send_to_hub("/join $data");
});

Irssi::command_bind('.mpart', sub {
    my ($data, $server, $witem) = @_;
    if (split(' ', $data) < 1) {
        Irssi::active_win()->print("%MBłąd.%Y Prawidłowe użycie:%W /.mpart #kanał");
        return;
    }
    send_to_hub("/part $data");
});

Irssi::command_bind('.kick', sub {
    my ($data, $server, $witem) = @_;
    if (split(' ', $data) < 2) {
        Irssi::active_win()->print("%MBłąd.%Y Prawidłowe użycie:%W /.kick #kanał nick (powód)");
        return;
    }
    send_to_hub("do_action kick $data");
});

Irssi::command_bind('.mmsg', sub {
    my ($data, $server, $witem) = @_;
    if (split(' ', $data) < 2) {
        Irssi::active_win()->print("%MBłąd.%Y Prawidłowe użycie:%W /.mmsg #kanał/nick tekst");
        return;
    }
    send_to_hub("/msg $data");
});

Irssi::command_bind('.mcycle', sub {
    my ($data, $server, $witem) = @_;
    if (split(' ', $data) < 1) {
        Irssi::active_win()->print("%MBłąd.%Y Prawidłowe użycie:%W /.mcycle #kanał (opcjonalnie tekst)");
        return;
    }
    send_to_hub("/cycle $data");
});

Irssi::command_bind('.op', sub {
    my ($data, $server, $witem) = @_;
    if (split(' ', $data) < 2) {
        Irssi::active_win()->print("%MBłąd.%Y Prawidłowe użycie:%W /.op #kanał nick");
        return;
    }
    send_to_hub("do_action op $data");
});

Irssi::command_bind('.rjoin', sub {
    my ($data, $server, $witem) = @_;
    if (split(' ', $data) < 2) {
        Irssi::active_win()->print("%MBłąd.%Y Prawidłowe użycie:%W /.rjoin nickbota #kanał");
        return;
    }
    my ($bot, $channel) = split(' ', $data, 2);
    send_to_hub("= $bot /join $channel");
});

Irssi::command_bind('.rpart', sub {
    my ($data, $server, $witem) = @_;
    if (split(' ', $data) < 2) {
        Irssi::active_win()->print("%MBłąd.%Y Prawidłowe użycie:%W /.rpart nickbota #kanał");
        return;
    }
    my ($bot, $channel) = split(' ', $data, 2);
    send_to_hub("= $bot /part $channel");
});

Irssi::command_bind('.rdisc', sub {
    my ($data, $server, $witem) = @_;
    if (split(' ', $data) < 1) {
        Irssi::active_win()->print("%MBłąd.%Y Prawidłowe użycie:%W /.rdisc nickbota");
        return;
    }
    send_to_hub("= $data /disconnect");
});

Irssi::command_bind('.rc4', sub {
    my ($data, $server, $witem) = @_;
    if (split(' ', $data) < 2) {
        Irssi::active_win()->print("%MBłąd.%Y Prawidłowe użycie:%W /.rc4 nickbota adres_servera_irc_v4");
        return;
    }
    my ($bot, $server_ip) = split(' ', $data, 2);
    send_to_hub("= $bot /connect -4 $server_ip");
});

Irssi::command_bind('.rc6', sub {
    my ($data, $server, $witem) = @_;
    if (split(' ', $data) < 2) {
        Irssi::active_win()->print("%MBłąd.%Y Prawidłowe użycie:%W /.rc6 nickbota adres_servera_irc_v6");
        return;
    }
    my ($bot, $server_ip) = split(' ', $data, 2);
    send_to_hub("= $bot /connect -6 $server_ip");
});

Irssi::command_bind('.addbot', sub {
    my ($data, $server, $witem) = @_;
    if (split(' ', $data) < 2) {
        Irssi::active_win()->print("%MBłąd.%Y Prawidłowe użycie:%W /.addbot nickbota ipv4_maszyny");
        return;
    }
    my ($bot, $ip) = split(' ', $data, 2);
    send_to_hub(".+bot $bot $ip $password");
});

Irssi::command_bind('.rmbot', sub {
    my ($data, $server, $witem) = @_;
    if (split(' ', $data) < 1) {
        Irssi::active_win()->print("%MBłąd.%Y Prawidłowe użycie:%W /.rmbot nickbota");
        return;
    }
    send_to_hub(".-bot $data");
});

Irssi::command_bind('.dodaj', sub {
    my ($data, $server, $witem) = @_;
    if (split(' ', $data) < 1) {
        Irssi::active_win()->print("%MBłąd.%Y Prawidłowe użycie:%W /.dodaj plik.pl");
        return;
    }
    send_to_hub("/dodaj $data");
});

Irssi::command_bind('.wywal', sub {
    my ($data, $server, $witem) = @_;
    if (split(' ', $data) < 1) {
        Irssi::active_win()->print("%MBłąd.%Y Prawidłowe użycie:%W /.wywal plik.pl");
        return;
    }
    send_to_hub("/wywal $data");
});

Irssi::command_bind('.rdodaj', sub {
    my ($data, $server, $witem) = @_;
    if (split(' ', $data) < 2) {
        Irssi::active_win()->print("%MBłąd.%Y Prawidłowe użycie:%W /.rdodaj nick_bota plik.pl");
        return;
    }
    my ($bot, $file) = split(' ', $data, 2);
    send_to_hub("= $bot /dodaj $file");
});

Irssi::command_bind('.rwywal', sub {
    my ($data, $server, $witem) = @_;
    if (split(' ', $data) < 2) {
        Irssi::active_win()->print("%MBłąd.%Y Prawidłowe użycie:%W /.rwywal nick_bota plik.pl");
        return;
    }
    my ($bot, $file) = split(' ', $data, 2);
    send_to_hub("= $bot /wywal $file");
});
############################
####### Komendy stop #######
############################

Irssi::command_bind('.help', sub {
    my $help_text = <<'HELP';

%M[Dostępne komendy]
%G /.mjoin #kanał%G - %Wwszystkie sesje wchodzą na #kanał
%G /.mpart #kanał%G - %Wwszystkie sesje wychodzą z #kanał
%G /.kick #kanał nick (powód)%G - %Wjeden losowy bot kopie nick z #kanał
%G /.bots%G - %Wpokazuje listę botów
%G /.admins%G - %Wpokazuje listę adminów
%G /.listall%G - %Wpokazuje listę botów z kanałami na których się znajdują
%G /.mmsg #kanał/nick tekst%G - %Wwysyła mass msg na #kanał lub do nicka
%G /.mcycle #kanał (opcjonalnie teskts)%G - %Wrobi cycle na #kanał
%G /.op #kanał nick%G - %Wlosowy bot daje opa nick na #kanał
%G /.rjoin nickbota #kanał%G - %Wdany bot wchodzi na #kanał
%G /.rpart nickbota #kanał%G - %Wdany bot wychodzi z #kanał
%G /.rdisc nickbota%G - %Wrozłącza danego bota z irc
%G /.rc4 nickbota adres_servera_irc_v4%G - %Włączy z danym serverem irc po ipv4
%G /.rc6 nickbota adres_servera_irc_v6%G - %Włączy z danym serverem irc po ipv6
%G /.addbot nickbota_ze_zmiennej_nick_w_irssi ipv4_maszyny_z_ktorej_irssi_sie_laczy/zl
%G /.rmbot nickbota%G - %Wusuwa bota
%G /.dodaj plik.pl%G - %Wściąga i ładuje skrypt u wszystkich (musi być wcześniej na naszym serwie)
%G /.wywal plik.pl%G - %Wuswa plik i unloaduje skrypt u wszystkich
%G /.rdodaj nick_bota plik.pl%G - %Wściąga i ładuje skrypt tylko u nick_bota (musi być wcześniej na naszym serwie)
%G /.rwywal nick_bota plik.pl%G - %Wuswa plik i unloaduje skrypt tylko u nick_bota

%M [Dostępne komendy dla skrypty AccessList (acl.pl)]
 %G/aop add [channel] [nick] [hostmask]%G - %WDodaje aop dla nicka i hostmasku
 %G/aop del [channel] [nick] [hostmask]%G - %WUsuwa aop dla nicka i hostmasku
 %G/aop list%G - %WWyświetla listę aop
 %G/shit add [channel] [nick] [recognition_host] [banmask] [kick_reason]%G - %WDodaje shit dla nicka
 %G/shit del [channel] [nick] [recognition_host]%G - %WUsuwa shit dla nicka
 %G/shit list%G - %WWyświetla listę shit

HELP
    if (Irssi::settings_get_bool('hub_print_aw')) {
        Irssi::active_win()->print($help_text);
    } else {
        Irssi::print($help_text);
    }
});


Irssi::print("Skrypt z komendami został załadowany!");
