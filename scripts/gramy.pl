use strict;
use warnings;
use Irssi;

use vars qw($VERSION %IRSSI);

$VERSION = "1.0";
%IRSSI = (
    authors     => 'themeTEST',
    contact     => '<EMAIL>',
    name        => 'gramy',
    description => 'Ogłasza grę na kanale po komendzie !gramy',
    license     => 'Public Domain',
);

Irssi::signal_add('message public', 'sig_message');

sub sig_message {
    my ($server, $msg, $nick, $address, $target) = @_;

    if ($msg =~ /^!gramy\b/i) {
        my $komunikat =
            "⚠️ 🤡 ⚠️  UWAGA FRAJERZY!!  ⚠️ 🤡 ⚠️\n" .
            "Mam ważny komunikat!!\n" .
            "🎮  Gramy w DMZ!  🎮\n" .
            "📹  stream się pojawi jak operator włączy kamerę :P";
        foreach my $line (split /\n/, $komunikat) {
            $server->command("MSG $target $line");
        }
    }
}

Irssi::print("Script gramy.pl loaded. Komenda !gramy ogłasza komunikat na kanale."); 