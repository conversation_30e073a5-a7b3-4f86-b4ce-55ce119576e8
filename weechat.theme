replaces = { };

abstracts = {
  # Minimal, high-contrast, weechat-like layout
  line_start = "";
  timestamp = "%K[%n$*%K]%n";

  hilight = "%W$*%n";
  error = "%R$*%n";
  channel = "%B%_$*%_";
  channelhilight = "%B$*%n";
  nick = "%n$*%n";
  nickhost = "$*";
  server = "%c$*%n";
  comment = "%K[$*]%n";
  reason = "%K($* )%n";
  mode = "%K<%g$*%K>%n";

  # People/events
  channick_hilight = "%C$*%n";
  channick_hilight2 = "%C$*%n";
  chanhost_hilight = "%K<%n{nickhost $*}%K>%n";
  channick = "%c$*%n";
  chanhost = "%K<%n{nickhost $*}%K>%n";

  # Messages – draw a clean nick column with a thin separator
  # $0 = nick mode, $1 = nick
  msgnick = "%N$0$1%n %K│%n %|";
  ownmsgnick = "%N$0$1%n %K│%n %|";
  ownnick = "%_$*%n";
  pubmsgnick = "{msgnick $0 $1-}";
  pubnick = "%N$*%n";
  pubmsgmenick = "{msgnick $0 $1-}";
  menick = "%Y$*%n";
  pubmsghinick = "{msgnick $1 $0$2-}";
  msgchannel = "%K:%B$*%n";

  # Private
  privmsg = "%K[%n$0%K(%n{nickhost $1-}%K)]%n ";
  ownprivmsg = "%K[%nmsg%K(%n$1-%K)]%n ";
  ownprivmsgnick = "%N$*%n %K│%n ";
  ownprivnick = "%_$*%n";
  privmsgnick = "%N$*%n %K│%n ";

  # Actions/CTCP/Notices
  action = "%K*%n";
  ownaction = "{action } $0 $1-";
  pvtaction = "%K[%nquery%K]%n {action } -> $1-";
  pvtaction_query = "%K*%n $* {action } ";
  pubaction = "{action } $*";

  ownnotice = "%K--%n notice to %K(%n$1%K)%n -- ";
  notice = "%K--%n notice %K--%n ";
  pvtnotice_host = "%K(%n$*%K)%n";
  servernotice = "{notice $*}";
  ownctcp = "%K[%nCTCP %c$0%K(%n$1%K)]%n ";
  ctcp = "%c$*%n";

  # Lists / DCC
  names_nick = "%C$0%w$[9]1-%n ";
  names_users = "(%c$0%w(%B$1%W))";
  names_channel = "%B$*%n";
  dcc = "%c$0%n $1 $3 $4 %c$5 $6 $7 $8-%n";
  dccfile = "%_$*%_";

  # Statusbar – neutral, subtle
  sb_background = "%0";
  sb = "%K[%n$0%K]%n";
  sbmode = "$0-";
  sbservertag = ":$0";
  sb_act_sep = "%K/";
  sb_act_text = "%c$*";
  sb_act_msg = "%G$*";
  sb_act_hilight = "%M$*";
  sb_act_hilight_color = "$0$1-%n";

  # Items used by statusbar config
  sbstart = "";
  sbend = " ";
  topicsbstart = "{sbstart $*}";
  topicsbend = "{sbend $*}";
  prompt = "[$*] ";

  # For nm2/nickcolor integration
  nickalign = "";
  nickcolor = "%n";
  nicktrunc = "";
  cumode_space = " ";
};

formats = {
  "fe-common/core" = {
    join = "%g→%n {channick_hilight $0} {chanhost_hilight $1}";
    part = "%r←%n {channick_hilight2 $0} {chanhost_hilight $1} {reason $3}";
    kick = "%R×%n {channick_hilight2 $0} by {nick $2} {reason $3}";
    quit = "%R×%n {channick_hilight2 $0} {chanhost_hilight $1} {reason $2}";
    servertag = "%K[%n$0%K]%n ";
    invite = "%gInvite%n {nick $0} to {channel $1}";
    new_topic = "%YTopic%n by {channick $0} in {channel $1}: $2";
    topic_unset = "%YTopic unset%n for {channel $1} by {channick $0}";

    # nm2/nickcolor aware public/own messages
    pubmsg = "{pubmsgnick $nickalign$2 {pubnick $nickcolor$0$nicktrunc}}$1";
    own_msg = "{ownmsgnick $nickalign$2 {ownnick $nickcolor$0$nicktrunc}}$1";
    own_msg_channel = "{ownmsgnick $nickalign$3 {ownnick $nickcolor$0$nicktrunc}{msgchannel $1}}$2";
    pubmsg_me = "{pubmsgmenick $nickalign$2 {menick $0$nicktrunc}}$1";
    pubmsg_me_channel = "{pubmsgmenick $nickalign$3 {menick $0$nicktrunc}{msgchannel $1}}$2";
    pubmsg_hilight = "{pubmsghinick $0 $nickalign$3 $1$nicktrunc}$2";
    pubmsg_hilight_channel = "{pubmsghinick $0 $nickalign$4 $1$nicktrunc{msgchannel $2}}$3";
    pubmsg_channel = "{pubmsgnick $nickalign$3 {pubnick $nickcolor$0$nicktrunc}{msgchannel $1}}$2";
    own_msg_private_query = "{ownprivmsgnick $nickalign{ownprivnick $nickcolor$2$nicktrunc}}$1";
    msg_private_query = "{privmsgnick $nickalign$nickcolor$0$nicktrunc}$2";
  };

  "fe-common/irc" = {
    inviting = "%g»%n Inviting {nick $0} to {channel $1}";
    topic_info = "%YTopic%n set by {channick $0} {comment $1}";
    server_chanmode_change = "%cServerMode%n/{channelhilight $0} {mode $1} by {nick $2}";
    whois = "{nick $0} ({nickhost $1%R@%n$2}) {comment $4}%:{whois %_i%_rcname $3}";
    whois_channels = "{whois %_c%_hannels %|$1}";
    end_of_whois = "%K[%nEnd of WHOIS%K]%n";
    who = "{channelhilight %C$[!10]0%n} %|{nick $[!9]1} %c$[!3]2%n $[!2]3 $4%R@%n$5 :: $6";
    ctcp_requested = "requested $2 from {nick $3} {ctcp {hilight $0} {comment $1}}";
    own_action = "$nickalign{ownaction $0$nicktrunc}$1";
    action_private = "$nickalign{pvtaction $0$nicktrunc}$2";
    action_private_query = "$nickalign{pvtaction_query $0$nicktrunc}$2";
    action_public = "$nickalign{pubaction $0$nicktrunc}$1";
  };

  nm2 = { neat_pad_char = " "; };
  "Irssi::Script::adv_windowlist" = {
    awl_display_key_active = "%0$N %K│%n$H$C$S";
    awl_display_key = "$N %K│%n$H$C$S";
    awl_viewer_item_bg = "%N";
    awl_display_header = "";
    awl_display_nokey = "$N %K│%n$H$C$S";
    awl_display_nokey_visible = "$N %K│%n$H$C$S";
    awl_display_key_visible = "$N %K│%n$H$C$S";
    awl_display_nokey_active = "%0$N %K│%n$H$C$S";
  };
  "Irssi::Script::nm2" = { neat_pad_char = " "; };
};


