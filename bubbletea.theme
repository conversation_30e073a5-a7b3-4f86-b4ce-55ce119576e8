
# bubbletea.theme
# Inspired by the soothing colors of bubble tea and the elegance of lipgloss.

replaces = {
  ":()" = "%K$*%n";
};

abstracts = {
  # General
  line_start = "%M♡%n ";
  timestamp = "%m➜ %c$*%n%|";
  error = "%rError: $*$*%n";
  hilight = "%M$*%n";

  # Channels and Nicks
  channel = "%c%_$*%_";
  nick = "%m$*%n";
  ownnick = "%Y%_$*%_";
  nickhost = "$*";
  channick = "%m$*%n";
  chanhost = " %M<%m{nickhost $*}%M>%n";
  channick_hilight = "%M$*%n";
  chanhost_hilight = " %M<{nickhost $*}%M>";

  # Messages
  msgnick = "%m$0%M:%n ";
  ownmsgnick = "%Y$0%M:%n ";
  pubmsgnick = "{msgnick $0 $1-}";
  pubnick = "%_$*%_";
  pubmsgmenick = "{msgnick $0 %_$1-%_%n}";
  menick = "%M$*%n";
  pubmsghinick = "{msgnick $1 $0$2-}";
  msgchannel = ":%c%_$*%_";
  privmsg = "%M[%c$0%M(%m{nickhost $1-}%M)]%n ";
  ownprivmsg = "%M[%Yquery%M(%y$1-%M)]%n ";

  # Actions
  action = "%M*%n";
  ownaction = "{action} %c$0 $1-";
  pvtaction = "%M[%cquery%M(%m$0%M)]%n {action} %m->%n $1-";
  pubaction = "{action} %c$* } %m";

  # System Messages
  join = "✨ %c$0%n {chanhost $1}";
  part = "👋 %c$0%n {chanhost $1} {reason $3}";
  kick = "💥 %c$0%n kicked from %c$2%n by %m$3%n {reason $4}";
  quit = "😔 %c$0%n {chanhost $1} {reason $2}";
  invite = "💌 %m$0%n invites you to {channel $1}";
  topic = "📜 Topic for %c$0%n is: $1";
  new_topic = "✍️ %m$0%n changed the topic in %c$1%n to: $2";
  topic_unset = "💨 %m$0%n unset the topic in %c$1%n";
  nick_changed = "🎭 {channick %m$0} is now {channick_hilight %M$1}";
  your_nick_changed = "💅 Your nick is now %M{nick $1}";
  mode = "🔧 Mode %c$0%n [%M$1%n] by {nick $2}";

  # Status Bar
  sb_background = "%m";
  sb_topic_bg = "%M";
  sb = "%c$0-%n";
  prompt = "%M♡%n ";
  sbmode = "$0-";
  sbaway = " %M(%maway%M)%n";
  sbservertag = "$0";
  sbmore = " %M…%n";
  sblag = " %rLag: $0-%n";
  sb_act_sep = "/";
  sb_act_text = "%M$*";
  sb_act_msg = "%c$*";
  sb_act_hilight = "%Y$*";

  # Colors
  # %c - cyan (light blue)
  # %m - magenta (pink)
  # %y - yellow
  # %r - red
  # %M - bold magenta
  # %Y - bold yellow
  # %C - bold cyan
  # %R - bold red
};

formats = {
  "fe-common/core" = {
    join = "{join $0 $1}";
    part = "{part $0 $1 $3}";
    kick = "{kick $1 $0 $2 $3}";
    quit = "{quit $0 $1 $2}";
    invite = "{invite $0 $1}";
    topic = "{topic $0 $1}";
    new_topic = "{new_topic $0 $1 $2}";
    topic_unset = "{topic_unset $0 $1}";
    your_nick_changed = "{your_nick_changed $0 $1}";
    nick_changed = "{nick_changed $0 $1}";
    talking_in = "🗣️ You are now talking in {channel $0}";
    not_in_channels = "🤷 You are not on any channels";
    names = "👥 Users in {channel $0}: $1";
    endofnames = "Total of $1 nicks ($2 ops, $4 voices)";
    mode = "{mode $0 $1 $2}";

    pubmsg = "{pubmsgnick $2 {pubnick $0}}$1";
    own_msg = "{ownmsgnick $2 {ownnick $0}}$1";
    own_msg_channel = "{ownmsgnick $3 {ownnick $0}{msgchannel $1}}$2";
    pubmsg_me = "{pubmsgmenick $2 {menick $0}}$1";
    pubmsg_me_channel = "{pubmsgmenick $3 {menick $0}{msgchannel $1}}$2";
    pubmsg_hilight = "{pubmsghinick $0 $3 $1}$2";
    pubmsg_hilight_channel = "{pubmsghinick $0 $4 $1{msgchannel $2}}$3";
    pubmsg_channel = "{pubmsgnick $3 {pubnick $0}{msgchannel $1}}$2";
    own_msg_private_query = "{ownprivmsgnick {ownprivnick $2}}$1";
    msg_private_query = "{privmsgnick $0}$2";
  };
};
