# Dokumentacja Konfiguracji Irssi

*Szczegółowa analiza struktury konfiguracji irssi z motywem Nexus Steel*

---

## Przegląd Struktury

Konfiguracja irssi składa się z następujących głównych sekcji:

1. **servers** - Definicje serwerów IRC
2. **aliases** - Rozbudowany zestaw aliasów (300+ linii)
3. **statusbar** - Konfiguracja paska statusu
4. **settings** - Ustawienia systemowe i skryptów
5. **chatnets** - Definicje sieci IRC
6. **keyboard** - Mapowania klawiszy
7. **windows** - Konfiguracja okien
8. **ignores** - Lista ignorowanych użytkowników
9. **startup** - Komendy startowe

---

## 1. Sekcja Servers

### Struktura
```irssi
servers = (
  {
    address = "sasl.irc.atw.hu";
    chatnet = "IRCnet";
    port = "6697";
    autoconnect = "no";
    use_ssl = "yes";
    use_tls = "yes";
    tls_verify = "yes";
    password = "";
    nick = "yooz";
    username = "y";
    realname = "https://www.ircnet.pl";
  },
  # ... więcej serwerów
);
```

### Kluczowe Elementy
- **Dwie główne sieci**: IRCnet i IRCnet2
- **SSL/TLS**: Wszystkie główne serwery używają szyfrowania
- **Backup serwery**: Nieszyfrowane serwery jako fallback
- **Różne realname**: IRCnet używa "https://www.ircnet.pl", IRCnet2 używa "https://www.al"

### Serwery Skonfigurowane
1. **sasl.irc.atw.hu:6697** (IRCnet, SSL)
2. **IRC.al:6697** (IRCnet2, SSL)
3. **irc.ircnet.com:6667** (IRCnet, backup)
4. **irc.ircnet.org:6667** (IRCnet, backup)

---

## 2. Sekcja Aliases

### Kategorie Aliasów

#### Podstawowe IRC (55-92)
```irssi
J = "join";
LEAVE = "part";
M = "msg";
K = "kick";
```

#### Zarządzanie Lagiem (98-101)
```irssi
lagsens = "set lag_check_time 1s; set lag_min_show 0.01s";
lagnorm = "set lag_check_time 10s; set lag_min_show 1s";
```

#### Multi-Network Management (153-193)
```irssi
NET1 = "network IRCnet; set real_name https://www.ircnet.pl";
NET2 = "network IRCnet2; set real_name https://www.al";
CONN1 = "connect IRCnet; set real_name https://www.ircnet.pl";
J1 = "network IRCnet; join $*";
MSG1 = "network IRCnet; msg $*";
```

#### Zarządzanie Skryptami (197-202)
```irssi
RELOADALL = "script reload *";
SCRIPTLIST = "script list";
SCRIPTLOAD = "script load $0";
```

#### Narzędzia Systemowe (273-305)
```irssi
SYSINFO = "exec -o - uname -a";
PING = "exec -o - ping -c 3 $*";
WEATHER = "exec -o - curl wttr.in/$*";
```

#### Tłumaczenia i Sprawdzanie Pisowni (204-221)
```irssi
TREN = "tr en $*";
TRPL = "tr pl $*";
SPELL = "spellcheck $*";
```

---

## 3. Sekcja Statusbar

### Struktura Items
```irssi
statusbar = {
  items = {
    time = "{sb %B∞%n%W❱ %G⚡%n Connected: %G%W$tag%n %W>%B>%C> %WNexus %G<%B<%W<%W}";
    user = "{sb %WU%wser ⇋ %B$cumode%w$N%W : ➜ %WS%wtatus {sbmode (%C+$usermode%n)}}";
    window = "%W ➜ %n{sbmode %WC%whannel (%C+$M%n%W) %B ➜ %WW%window: %G$winref }{sbaway $A}%W  %n";
    prompt = "%w⟨%B$itemname%w⟩: ";
    prompt_empty = "⟨%B$winname%n⟩ ";
  };
};
```

### Konfiguracja Statusbar
- **window**: Wyłączony (disabled = "yes")
- **prompt**: Aktywny na dole (position = "100")
- **topic**: Na górze (position = "1")
- **info1**: Informacje użytkownika i okna
- **info2**: Czas, aktywność, lag

### Integracja z Motywem Nexus Steel
- Używa kolorów motywu (%B, %G, %W, %C)
- UTF-8 ikony (∞, ⚡, ❱, ⇋, ➜)
- Cyberpunk styling z nawiasami ⟨⟩

---

## 4. Sekcja Settings

### Core Settings (391-409)
```irssi
core = {
  real_name = "https://www.al IRCnet2";
  user_name = "y";
  nick = "theme_test";
  timestamp_format = "%H:%M:%S";
  recode_out_default_charset = "utf8";
  recode_autodetect_utf8 = "yes";
  proxy_address = "************";
  proxy_password = "tahioN1";
  proxy_port = "3392";
};
```

### IRC Core Settings (410-419)
```irssi
"irc/core" = {
  lag_check_time = "5s";
  ctcp_version_reply = "";
  skip_motd = "no";
  lag_max_before_disconnect = "300";
  cmds_max_at_once = "10";
};
```

### Frontend Settings (420-435)
```irssi
"fe-common/core" = {
  theme = "nexus_steel";
  activity_hide_level = "KICK MODE TOPIC NICK QUIT...";
  timestamps = "yes";
  hilight_color = "%M";
  autolog = "yes";
  autolog_path = "~/.irssi/logs/$tag/$0.log";
  term_charset = "UTF-8";
  use_status_window = "no";
  window_auto_change = "yes";
};
```

### Perl Scripts Settings (436-470)
```irssi
"perl/core/scripts" = {
  rotator_chars = "%B%W⚡%k________%k %k_%k%W⚡%k_______%k...";
  rotator_speed = "180";
  nick_colors = "%r %R %g %G %y %b %B %c %C %X42...";
  spellcheck_default_language = "pl_PL";
  translate_api_key = "AIzaSyDUfcmoiserVvtS_jlQ7Tj9s7xM-6Fyti0";
  chansort_configurable_autosort = "yes";
};
```

### Text Frontend Settings (471-479)
```irssi
"fe-text" = {
  lag_min_show = "1s";
  scrollback_lines = "2000";
  colors = "yes";
  colors_ansi_24bit = "yes";
};
```

### Proxy Settings (481-486)
```irssi
proxy = {
  irssiproxy_password = "tahioN1";
  irssiproxy_ports = "IRCnet=3992";
  irssiproxy_bind = "0.0.0.0";
  irssiproxy = "yes";
};
```

---

## 5. Sekcja Chatnets

### Struktura
```irssi
chatnets = {
  IRCnet = {
    type = "IRC";
    sasl_mechanism = "PLAIN";
    sasl_username = "yooz";
    sasl_password = "mypass3000";
  };
  IRCnet2 = {
    type = "IRC";
    sasl_mechanism = "PLAIN";
    sasl_username = "yooz";
    sasl_password = "mypass3000";
  };
};
```

### Kluczowe Elementy
- **SASL Authentication**: Obie sieci używają SASL PLAIN
- **Wspólne dane**: Ten sam username i password dla obu sieci
- **Dodatkowe sieci**: Zdefiniowane ale nieużywane (UnderNET, EFnet, etc.)

---

## 6. Pozostałe Sekcje

### Keyboard (512-514)
```irssi
keyboard = ( 
  { key = "meta-[M"; id = "command"; data = "mouse_xterm "; }
);
```

### Windows (515-520)
```irssi
windows = {
  1 = {
    immortal = "yes";
    level = "ALL -PUBLIC -MSGS -ACTIONS -NOTICE -INVITES";
  };
};
```

### Ignores (521)
```irssi
ignores = ( { mask = "MrsEtCH"; level = "ALL"; } );
```

### Startup (522-524)
```irssi
startup = {
  command = "/set theme nexus_steel.theme; /set real_name https://www.ircnet.pl; /window new hidden; /window name Notices; /window level -ALL +NOTICES +INVITES; /window move 2; /window new hidden; /window name IRCnet; /network IRCnet; /window new hidden; /window name IRCnet2; /network IRCnet2; /set real_name https://www.al; /echo === Multi-Network IRC Setup Complete ===; /echo Theme: tahio.theme; /echo IRCnet realname: https://www.ircnet.pl; /echo IRCnet2 realname: https://www.al; /echo Use NET1/NET2 to switch networks; /echo Use OPHELP for IRCop commands; /echo Use HELP for quick help";
};
```

---

## Kluczowe Funkcjonalności

### Multi-Network Support
- Automatyczne przełączanie między IRCnet i IRCnet2
- Różne realname dla każdej sieci
- Dedykowane aliasy (NET1/NET2, J1/J2, MSG1/MSG2)

### Integracja z Motywem Nexus Steel
- Statusbar używa kolorów i ikon motywu
- UTF-8 support z cyberpunk styling
- 24-bit color support

### Proxy Configuration
- irssiproxy na porcie 3992
- Bind na wszystkich interfejsach (0.0.0.0)
- Hasło zabezpieczone

### Perl Scripts Integration
- Rotator z animowanymi ikonami ⚡
- Nick coloring z 16 kolorami
- Spellcheck w języku polskim
- Google Translate API integration
- Automatic channel sorting

### Logging i Monitoring
- Autolog wszystkich wiadomości
- Lag monitoring (5s intervals)
- UTF-8 encoding support
- 2000 linii scrollback

---

*Dokumentacja utworzona na podstawie analizy pliku `/Users/<USER>/.irssi/config`*
