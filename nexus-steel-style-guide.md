# Nexus Steel Style Guide

*The definitive reference for colors, icons, and visual elements in the Nexus Steel terminal theme.*

---

## Overview

Nexus Steel is a cyberpunk-inspired terminal theme that combines electric blues, neon greens, and industrial steel grays to create a futuristic, high-contrast interface. This guide defines the exact specifications for all visual elements.

---

## Color Palette

### 16-Color Terminal Mappings

| Code | Color Name | Hex Value | Usage Context |
|------|------------|-----------|---------------|
| `%0` | void-black | `#000000` | Background, shadows |
| `%1` | crimson-red | `#ff3366` | Errors, critical alerts |
| `%2` | neon-green | `#33ff66` | Success, active processes |
| `%3` | amber-yellow | `#ffcc00` | Warnings, pending states |
| `%4` | electric-blue | `#00aaff` | Primary accent, links |
| `%5` | plasma-purple | `#cc66ff` | Secondary accent, special |
| `%6` | cyan-steel | `#00ffcc` | Info, metadata |
| `%7` | silver-gray | `#cccccc` | Standard text, neutral |
| `%8` | shadow-gray | `#666666` | Disabled, comments |
| `%9` | hot-red | `#ff6699` | Error highlights |
| `%a` | lime-green | `#66ff99` | Success highlights |
| `%b` | gold-yellow | `#ffdd33` | Warning highlights |
| `%c` | sky-blue | `#33ccff` | Info highlights |
| `%d` | violet-purple | `#dd99ff` | Special highlights |
| `%e` | aqua-cyan | `#33ffdd` | Data highlights |
| `%f` | pure-white | `#ffffff` | Emphasis, headers |

### Extended Color Variants

#### Primary Colors (High Intensity)
- **Electric Blue Family**: `#0099ee`, `#00aaff`, `#11bbff`
- **Neon Green Family**: `#22ff55`, `#33ff66`, `#44ff77`
- **Steel Gray Family**: `#bbbbbb`, `#cccccc`, `#dddddd`

#### Accent Colors (Medium Intensity)
- **Crimson Red Family**: `#ee2255`, `#ff3366`, `#ff4477`
- **Plasma Purple Family**: `#bb55ff`, `#cc66ff`, `#dd77ff`
- **Cyan Steel Family**: `#00eecc`, `#00ffcc`, `#11ffdd`

---

## Usage Rules

### Event Type Color Assignments

#### System Events
- **Boot/Startup**: `%4` (electric-blue) + `%f` (pure-white)
- **Shutdown**: `%8` (shadow-gray) + `%0` (void-black)
- **Process Start**: `%2` (neon-green) + `⚡` icon
- **Process End**: `%6` (cyan-steel) + `◆` icon
- **System Alert**: `%1` (crimson-red) + `⚠` icon

#### Network Events
- **Connection Established**: `%2` (neon-green) + `▶` icon
- **Connection Lost**: `%1` (crimson-red) + `◀` icon
- **Data Transfer**: `%4` (electric-blue) + `⧐` icon
- **Network Info**: `%6` (cyan-steel) + `∞` icon

#### Application Events
- **Success Operations**: `%2` (neon-green) + `%a` (lime-green)
- **Error Conditions**: `%1` (crimson-red) + `%9` (hot-red)
- **Warning States**: `%3` (amber-yellow) + `%b` (gold-yellow)
- **Information**: `%4` (electric-blue) + `%c` (sky-blue)
- **Debug/Verbose**: `%8` (shadow-gray) + `%7` (silver-gray)

#### Security Events
- **Authentication Success**: `%2` (neon-green) + `⬢` icon
- **Authentication Failure**: `%1` (crimson-red) + `⚠` icon
- **Permission Granted**: `%5` (plasma-purple) + `◆` icon
- **Access Denied**: `%1` (crimson-red) + `◀` icon

### Color Combination Rules

#### High Contrast Pairs
✅ **Recommended**:
- `%f` (pure-white) on `%0` (void-black)
- `%2` (neon-green) on `%0` (void-black)
- `%4` (electric-blue) on `%0` (void-black)
- `%1` (crimson-red) on `%f` (pure-white)

❌ **Avoid**:
- `%3` (amber-yellow) on `%f` (pure-white) - Low contrast
- `%8` (shadow-gray) on `%0` (void-black) - Poor readability
- `%5` (plasma-purple) on `%1` (crimson-red) - Color clash

#### Semantic Color Groups
- **Success Chain**: `%2` → `%a` → `%e` (green-to-cyan progression)
- **Error Chain**: `%1` → `%9` → `%3` (red-to-amber escalation)
- **Info Chain**: `%4` → `%c` → `%6` (blue-to-cyan spectrum)
- **Neutral Chain**: `%7` → `%8` → `%f` (gray gradient)

---

## UTF-8 Icon Palette

### Core System Icons

| Icon | Unicode | Hex Code | Semantic Meaning | Usage Context |
|------|---------|----------|------------------|---------------|
| ⚡ | U+26A1 | `\u26A1` | Power, Energy, Process | System startup, active processes |
| ⟪ | U+27EA | `\u27EA` | Input, Beginning | Data input, session start |
| ⟫ | U+27EB | `\u27EB` | Output, End | Data output, session end |
| ⧐ | U+29D0 | `\u29D0` | Transfer, Flow | Data movement, network traffic |
| ▶ | U+25B6 | `\u25B6` | Start, Forward | Process start, connection |
| ◀ | U+25C0 | `\u25C0` | Stop, Backward | Process stop, disconnection |
| ◆ | U+25C6 | `\u25C6` | Component, Module | System components, modules |
| ⬢ | U+2B22 | `\u2B22` | Secure, Protected | Security, authentication |
| ⚙ | U+2699 | `\u2699` | Configuration, Settings | System config, preferences |
| ⚠ | U+26A0 | `\u26A0` | Warning, Attention | Warnings, alerts |
| ∞ | U+221E | `\u221E` | Infinite, Continuous | Services, persistent connections |

### Extended Icon Set

#### Status Indicators
| Icon | Unicode | Meaning | Color Pairing |
|------|---------|---------|---------------|
| ✓ | U+2713 | Success | `%2` (neon-green) |
| ✗ | U+2717 | Failure | `%1` (crimson-red) |
| ◯ | U+25CB | Pending | `%3` (amber-yellow) |
| ◉ | U+25C9 | Active | `%4` (electric-blue) |
| ◎ | U+25CE | Complete | `%2` (neon-green) |
| ⦿ | U+29BF | Error | `%1` (crimson-red) |

#### Directional Indicators
| Icon | Unicode | Meaning | Usage |
|------|---------|---------|-------|
| ↑ | U+2191 | Upload, Increase | Data upload, metrics up |
| ↓ | U+2193 | Download, Decrease | Data download, metrics down |
| → | U+2192 | Forward, Next | Process flow, navigation |
| ← | U+2190 | Backward, Previous | Return, undo operations |
| ↔ | U+2194 | Bidirectional | Sync, two-way communication |
| ⇄ | U+21C4 | Exchange | Data exchange, swap |

#### Technical Symbols
| Icon | Unicode | Meaning | Context |
|------|---------|---------|---------|
| ⟦ | U+27E6 | Data Block Start | Log blocks, data structures |
| ⟧ | U+27E7 | Data Block End | Log blocks, data structures |
| ⟨ | U+27E8 | Parameter Start | Function parameters, config |
| ⟩ | U+27E9 | Parameter End | Function parameters, config |
| ▪ | U+25AA | List Item | Bullet points, lists |
| ▫ | U+25AB | Sub Item | Sub-bullets, nested items |
| ■ | U+25A0 | Solid Block | Progress bars, filled states |
| □ | U+25A1 | Empty Block | Progress bars, empty states |

### Icon Usage Guidelines

#### Size and Spacing
- **Standard Icons**: Use single-width Unicode characters
- **Spacing**: Always include one space after icon before text
- **Alignment**: Left-align icons in columnar displays

#### Color Combinations
- **Status Icons**: Match semantic color (success=green, error=red, etc.)
- **Neutral Icons**: Use `%7` (silver-gray) or `%8` (shadow-gray)
- **Emphasis Icons**: Use `%4` (electric-blue) or `%f` (pure-white)

#### Context-Specific Usage
```
System Events:  ⚡ %2[BOOT]%f System initialization complete
Network Events: ⧐ %4[NET]%f Data transfer: 1.2MB/s
Security Events: ⬢ %2[AUTH]%f User authenticated successfully
Error Events:   ⚠ %1[ERROR]%f Connection timeout after 30s
```

---

## Implementation Examples

### Log Entry Formats
```
[%4⚡%f] %2SYSTEM%f ⟪ Boot sequence initiated
[%6⧐%f] %4NETWORK%f → Connection established to 192.168.1.1
[%1⚠%f] %1ERROR%f ⦿ Failed to load configuration file
[%2✓%f] %2SUCCESS%f ◎ All services running normally
```

### Status Display Patterns
```
%4◉%f Active Processes: %23
%2◎%f Completed Tasks: %156
%3◯%f Pending Jobs: %7
%1⦿%f Failed Operations: %2
```

### Progress Indicators
```
Progress: %2■■■■■%8□□□□□%f 50% Complete
Transfer: %4⧐%f %aUploading%f ↑ 2.4MB/s %6∞%f
Security: %5⬢%f %2Authenticated%f ✓ Session active
```

---

## Accessibility Considerations

### High Contrast Mode
- **Background**: Always `%0` (void-black)
- **Primary Text**: `%f` (pure-white)
- **Accent Colors**: Limited to `%2`, `%1`, `%4` for maximum contrast

### Color Blind Support
- **Never rely on color alone** for critical information
- **Always pair colors with icons** for semantic meaning
- **Use distinct brightness levels** for different states

### Terminal Compatibility
- **Fallback Colors**: Provide 8-color fallbacks for older terminals
- **Icon Fallbacks**: ASCII alternatives for non-UTF-8 terminals
- **Test Coverage**: Verify appearance in major terminal emulators

---

## Version History

- **v1.0.0** - Initial Nexus Steel style guide
- Colors, icons, and usage rules established
- Single source of truth for all visual elements

---

*This document serves as the canonical reference for all Nexus Steel implementations. Any deviations from these specifications must be documented and approved through the standard change process.*
