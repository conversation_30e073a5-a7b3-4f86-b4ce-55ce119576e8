# nerdcore.theme
# A comprehensive nerd/geek theme with a high-contrast palette and detailed format definitions.

replaces = { ":()" = "%K$*%n"; };

abstracts = {
  # General
  line_start = "%B»%n ";
  timestamp = "%g➜ %b$*%n%|";
  error = "%RError: $*$*%n";
  hilight = "%Y$*%n";

  # Channels and Nicks
  channel = "%g%_$*%_";
  nick = "%b$*%n";
  ownnick = "%Y%_$*%_";
  nickhost = "$*";
  channick = "%g$*%n";
  chanhost = " %B[%g{nickhost $*}%B]%n";
  channick_hilight = "%Y$*%n";
  chanhost_hilight = " %B[{nickhost $*}]%B";

  # Messages
  msgnick = "%b$0%n%B:%n ";
  ownmsgnick = "%Y$0%n%B:%n ";
  pubmsgnick = "{msgnick $0 $1-}";
  pubnick = "%_$*%_";
  pubmsgmenick = "{msgnick $0 %_$1-%_%n}";
  menick = "%Y$*%n";
  pubmsghinick = "{msgnick $1 $0$2-}";
  msgchannel = ":%g%_$*%_";
  privmsg = "%B[%g$0%B(%b{nickhost $1-}%B)]%n ";
  ownprivmsg = "%B[%Yquery%B(%y$1-%B)]%n ";

  # Actions
  action = "%Y*%n";
  ownaction = "{action} %g$0 $1-";
  pvtaction = "%B[%gquery%B(%b$0%B)]%n {action} %b->%n $1-";
  pubaction = "{action} %g$* } %b";

  # System Messages
  join = "%G»»%n {channick_hilight %g$0} {chanhost $1}";
  part = "%Y««%n {channick_hilight %y$0} {chanhost $1} {reason $3}";
  kick = "%R✗%n {channick_hilight %B$1} kicked from {channel %B$0} by {nick %B$2} {reason $3}";
  quit = "%r↤%n {channick %y$0} {chanhost $1} {reason $2}";
  invite = "%g✉%n {nick %b$0} invites you to {channel %g$1}";
  topic = "📜 Topic for {channel %g$0} is: $1";
  new_topic = "✍️ {nick %b$0} changed the topic in {channel %g$1} to: $2";
  topic_unset = "💨 {nick %b$0} unset the topic in {channel %g$1}";
  nick_changed = "👤 {channick %b$0} is now {channick_hilight %Y$1}";
  your_nick_changed = "👤 Your nick is now %Y{nick $1}";
  mode = "🔧 Mode {channel %g$0} [%Y$1%n] by {nick %b$2}";

  # Status Bar
  sb_background = "%b";
  sb_topic_bg = "%B";
  sb = "%g$0-%n";
  prompt = "%B»%n ";
  sbmode = "$0-";
  sbaway = " %Y(away)%n";
  sbservertag = "$0";
  sbmore = " %B…%n";
  sblag = " %Rlag: $0-%n";
  sb_act_sep = "/";
  sb_act_text = "%Y$*";
  sb_act_msg = "%g$*";
  sb_act_hilight = "%R$*";

  # Colors
  # %g, %G - green, bold green (terminal green)
  # %b, %B - blue, bold blue (electric blue)
  # %y, %Y - yellow, bold yellow (vibrant orange)
  # %r, %R - red, bold red (errors)
};

formats = {
  "fe-common/core" = {
    join = "{join $0 $1}";
    part = "{part $0 $1 $3}";
    kick = "{kick $0 $1 $2 $3}";
    quit = "{quit $0 $1 $2}";
    invite = "{invite $0 $1}";
    topic = "{topic $0 $1}";
    new_topic = "{new_topic $0 $1 $2}";
    topic_unset = "{topic_unset $0 $1}";
    your_nick_changed = "{your_nick_changed $0 $1}";
    nick_changed = "{nick_changed $0 $1}";
    talking_in = "🗣️ You are now talking in {channel $0}";
    not_in_channels = "🤷 You are not on any channels";
    names = "{names_users %W ➜ Users ☣ ) (channel {names_channel $0}} ➜ (%G$1) ☣ ";
    names_nick = "{names_nick $0 $1}";
    endofnames = "{channel %w$0}: %WTotal of {hilight %B((%G$1%R))%W} %wnicks%n {comment %WOps {hilight %W((%G$2%W))%W}, Voice %B(({hilight %G$4%R))%W}, Normal %B(({hilight %G$5%R))%W}";
    mode = "{mode $0 $1 $2}";

    pubmsg = "{pubmsgnick $nickalign$2 {pubnick $nickcolor$0$nicktrunc}}$1";
    own_msg = "{ownmsgnick $nickalign$2 {ownnick $nickcolor$[.9]0$nicktrunc}}$1";
    own_msg_channel = "{ownmsgnick $nickalign$3 {ownnick $nickcolor$[.9]0$nicktrunc}{msgchannel $1}}$2";
    pubmsg_me = "{pubmsgmenick $nickalign$2 {menick $0$nicktrunc}}$1";
    pubmsg_me_channel = "{pubmsgmenick $nickalign$3 {menick $0$nicktrunc}{msgchannel $1}}$2";
    pubmsg_hilight = "{pubmsghinick $0 $nickalign$3 $1$nicktrunc}$2";
    pubmsg_hilight_channel = "{pubmsghinick $0 $nickalign$4 $1$nicktrunc{msgchannel $2}}$3";
    pubmsg_channel = "{pubmsgnick $nickalign$3 {pubnick $nickcolor$0$nicktrunc}{msgchannel $1}}$2";
    own_msg_private_query = "{ownprivmsgnick $nickalign{ownprivnick $nickcolor$2$nicktrunc}}$1";
    msg_private_query = "{privmsgnick $nickalign$nickcolor$0$nicktrunc}$2";
  };
  "fe-common/irc" = {
    inviting = "%K>%G>%W>%n Inviting {nick $0} to {channel $1}";
    topic_info = "%Kmode   %nTopic set by %N{channick $0} {comment $1}";
    server_chanmode_change = "{netsplit %CServerMode%n}/{channelhilight $0} {mode $1} by {nick $2}";
    whois = "{nick $0} ({nickhost $1%R@%n$2}) {comment $4}%:{whois %_i%_rcname $3}";
    whowas = "           %WWHOWAS%K       %n%:%r*%n {nick $0} ({nickhost $1%R@%n$2})%:{whois %_i%_rcname $3}";
    whois_idle = "{whois3 %_i%_dle %|$1 days $2 hours $3 mins $4 secs}";
    whois_idle_signon = "{whois3 %_i%_dle %|$1 days $2 hours $3 mins $4 secs (signon: $5)}";
    whois_server = "{whois2 %_s%_erver %|$1 ($2)}";
    whois_oper = "{whois3 %_o%_perator {hilight $1}}";
    whois_registered = "{whois %_a%_uth has registered this nick}";
    whois_help = "{whois %_d%_uty is available for help}";
    whois_modes = " {whois %_m%_odes $1}";
    whois_realhost = "{whois %_h%_ostname $1-}";
    whois_usermode = "{whois %_u%_sermode $1}";
    whois_channels = "{whois %_c%_hannels %|$1}";
    whois_away = "{whois %_a%_way %W%|$1}";
    whois_special = "{whois  %|{hilight $1}}";
    end_of_whois = "%WEnd of WHOIS ☣ ";
    end_of_whowas = "%wEnd of WHOWAS ☣ ";
    whois_not_found = "%wThere is no such Nick:%R.%w {nick %W$0}";
    who = "{channelhilight %C$[!10]0%n} %|{nick $[!9]1} %c$[!3]2%n $[!2]3 $4%R@%n$5 :: $6";
    ctcp_requested = "requested $2 from {nick $3} {ctcp {hilight $0} {comment $1}}";
    own_action = "$nickalign{ownaction $0$nicktrunc}$1";
    action_private = "$nickalign{pvtaction $0$nicktrunc}$2";
    action_private_query = "$nickalign{pvtaction_query $0$nicktrunc}$2";
    action_public = "$nickalign{pubaction $0$nicktrunc}$1";
  };
  "fe-common/irc/dcc" = {
    own_dcc = "{dccownmsg {dccownnick $1}}$2";
    own_dcc_action = "{dccownaction_target $0 $1}$2";
    own_dcc_action_query = "{dccownaction $0}$2";
    own_dcc_ctcp = "{ownctcp ctcp $0}$1 $2";
    dcc_msg = "{dccmsg $0}$1";
    action_dcc = "{dccaction $0}$1";
    action_dcc_query = "{dccaction $0}$1";
    own_dcc_query = "{dccownmsg {dccownnick $0}}$2";
    dcc_msg_query = "{dccmsg $0}$1";
    dcc_ctcp = "{dcc %W>>> DCC CTCP {hilight $1} received from {hilight $0}: $2}";
  };
  nm2 = { neat_pad_char = " "; };
  # Styles for adv_windowlist.pl (channel sidebar)
  "Irssi::Script::adv_windowlist" = {
    # Style for the active window
    awl_display_key_active = "%B» $N %g$H$C$S";

    # Style for inactive windows
    awl_display_key = "  %b$N %g$H$C$S";

    # Background for the viewer
    awl_viewer_item_bg = "%N";

    # Header for the list (empty)
    awl_display_header = "";

    # Style for windows without a hotkey
    awl_display_nokey = "  %b$N %g$H$C$S";

    # Style for visible windows (e.g., in a split view)
    awl_display_nokey_visible = "👁 %b$N %g$H$C$S";
    awl_display_key_visible = "👁 %b$N %g$H$C$S";

    # Style for the active window when it has no hotkey
    awl_display_nokey_active = "%B» $N %g$H$C$S";

    # Colors for different activity levels
    awl_data_level_none = "%g";
    # Default color (green)
    awl_data_level_low = "%y";
    # Low activity (messages)
    awl_data_level_medium = "%Y";
    # Medium activity (public messages)
    awl_data_level_high = "%R";
    # High activity (highlights)
  };
  "Irssi::Script::nm2" = { neat_pad_char = " "; };
};
